<?php
/**
 * AstroGenix - Create Admin User Script
 * Run this script to create the default admin user
 */

// Include configuration
require_once '../config/database.php';

// Admin credentials
$username = 'admin';
$email = '<EMAIL>';
$password = 'admin123'; // Change this to your desired password
$role = 'super_admin';

try {
    // Hash the password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    // Check if admin already exists
    $existing = fetchRow("SELECT id FROM admin_users WHERE username = ? OR email = ?", [$username, $email]);
    
    if ($existing) {
        echo "Admin user already exists!\n";
        echo "Username: {$username}\n";
        echo "Email: {$email}\n";
        echo "You can update the password if needed.\n";
    } else {
        // Insert admin user
        $sql = "INSERT INTO admin_users (username, email, password, role, is_active) VALUES (?, ?, ?, ?, 1)";
        $result = executeQuery($sql, [$username, $email, $hashedPassword, $role]);
        
        if ($result) {
            echo "Admin user created successfully!\n";
            echo "Username: {$username}\n";
            echo "Email: {$email}\n";
            echo "Password: {$password}\n";
            echo "\nPlease change the password after first login!\n";
        } else {
            echo "Failed to create admin user.\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
