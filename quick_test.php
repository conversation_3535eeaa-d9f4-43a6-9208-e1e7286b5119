<?php
/**
 * AstroGenix - Quick Test
 * Fast test to check if everything works
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>AstroGenix Quick Test</h1>";

try {
    // Include config
    require_once 'config/config.php';
    echo "<p style='color: green;'>✅ Configuration loaded successfully!</p>";
    
    // Test database
    $db = getDB();
    echo "<p style='color: green;'>✅ Database connection works!</p>";
    
    // Test functions
    if (function_exists('generateCSRFToken')) {
        $token = generateCSRFToken();
        echo "<p style='color: green;'>✅ CSRF token: " . substr($token, 0, 10) . "...</p>";
    }
    
    if (function_exists('getSetting')) {
        $siteName = getSetting('site_name', 'AstroGenix');
        echo "<p style='color: green;'>✅ Site name: $siteName</p>";
    }
    
    if (function_exists('getInvestmentPackages')) {
        $packages = getInvestmentPackages();
        echo "<p style='color: green;'>✅ Investment packages: " . count($packages) . " found</p>";
    }
    
    echo "<h2 style='color: green;'>🎉 Everything works!</h2>";
    echo "<p><strong>You can now:</strong></p>";
    echo "<ul>";
    echo "<li><a href='index.php'>Visit main page</a></li>";
    echo "<li><a href='login.php'>Login page</a></li>";
    echo "<li><a href='register.php'>Registration page</a></li>";
    echo "</ul>";
    
    echo "<h3>Admin Login:</h3>";
    echo "<p>Username: <strong>admin</strong></p>";
    echo "<p>Password: <strong>admin123</strong></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ Error:</h2>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    
    echo "<h3>Quick fixes:</h3>";
    echo "<ol>";
    echo "<li>Make sure database 'astrogenix' exists</li>";
    echo "<li>Import database/astrogenix_complete.sql</li>";
    echo "<li>Check database credentials in config/database.php</li>";
    echo "</ol>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>AstroGenix Quick Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: #f8fafc;
        }
        h1 { 
            color: #1e3a8a; 
            text-align: center;
        }
        h2, h3 { color: #374151; }
        p { margin: 10px 0; }
        ul, ol { 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        a {
            color: #1e3a8a;
            text-decoration: none;
            font-weight: bold;
        }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
</body>
</html>
