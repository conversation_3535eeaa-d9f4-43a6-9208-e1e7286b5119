<?php
/**
 * AstroGenix Security Functions
 * Comprehensive security measures and input validation
 */

// Prevent direct access
if (!defined('ROOT_PATH')) {
    die('Direct access not allowed');
}

/**
 * Sanitize input data
 */
function sanitizeInput($input, $type = 'string') {
    if (is_array($input)) {
        return array_map(function($item) use ($type) {
            return sanitizeInput($item, $type);
        }, $input);
    }
    
    // Remove null bytes
    $input = str_replace(chr(0), '', $input);
    
    switch ($type) {
        case 'email':
            return filter_var(trim($input), FILTER_SANITIZE_EMAIL);
            
        case 'url':
            return filter_var(trim($input), FILTER_SANITIZE_URL);
            
        case 'int':
            return filter_var($input, FILTER_SANITIZE_NUMBER_INT);
            
        case 'float':
            return filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
            
        case 'username':
            return preg_replace('/[^a-zA-Z0-9_]/', '', trim($input));
            
        case 'phone':
            return preg_replace('/[^0-9+\-\s()]/', '', trim($input));
            
        case 'alphanumeric':
            return preg_replace('/[^a-zA-Z0-9]/', '', trim($input));
            
        case 'html':
            return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
            
        default: // string
            return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}

/**
 * Validate input data
 */
function validateInput($input, $rules) {
    $errors = [];
    
    foreach ($rules as $field => $rule) {
        $value = isset($input[$field]) ? $input[$field] : '';
        $fieldRules = explode('|', $rule);
        
        foreach ($fieldRules as $fieldRule) {
            $ruleParts = explode(':', $fieldRule);
            $ruleName = $ruleParts[0];
            $ruleValue = isset($ruleParts[1]) ? $ruleParts[1] : null;
            
            switch ($ruleName) {
                case 'required':
                    if (empty($value)) {
                        $errors[$field][] = "Field {$field} is required";
                    }
                    break;
                    
                case 'email':
                    if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $errors[$field][] = "Field {$field} must be a valid email";
                    }
                    break;
                    
                case 'min':
                    if (!empty($value) && strlen($value) < $ruleValue) {
                        $errors[$field][] = "Field {$field} must be at least {$ruleValue} characters";
                    }
                    break;
                    
                case 'max':
                    if (!empty($value) && strlen($value) > $ruleValue) {
                        $errors[$field][] = "Field {$field} must not exceed {$ruleValue} characters";
                    }
                    break;
                    
                case 'numeric':
                    if (!empty($value) && !is_numeric($value)) {
                        $errors[$field][] = "Field {$field} must be numeric";
                    }
                    break;
                    
                case 'alpha':
                    if (!empty($value) && !preg_match('/^[a-zA-Z]+$/', $value)) {
                        $errors[$field][] = "Field {$field} must contain only letters";
                    }
                    break;
                    
                case 'alphanumeric':
                    if (!empty($value) && !preg_match('/^[a-zA-Z0-9]+$/', $value)) {
                        $errors[$field][] = "Field {$field} must contain only letters and numbers";
                    }
                    break;
                    
                case 'username':
                    if (!empty($value) && !preg_match('/^[a-zA-Z0-9_]{3,20}$/', $value)) {
                        $errors[$field][] = "Username must be 3-20 characters and contain only letters, numbers, and underscores";
                    }
                    break;
                    
                case 'password':
                    if (!empty($value)) {
                        if (strlen($value) < MIN_PASSWORD_LENGTH) {
                            $errors[$field][] = "Password must be at least " . MIN_PASSWORD_LENGTH . " characters";
                        }
                        if (!preg_match('/[A-Z]/', $value)) {
                            $errors[$field][] = "Password must contain at least one uppercase letter";
                        }
                        if (!preg_match('/[a-z]/', $value)) {
                            $errors[$field][] = "Password must contain at least one lowercase letter";
                        }
                        if (!preg_match('/[0-9]/', $value)) {
                            $errors[$field][] = "Password must contain at least one number";
                        }
                    }
                    break;
                    
                case 'unique':
                    if (!empty($value)) {
                        $tableParts = explode('.', $ruleValue);
                        $table = $tableParts[0];
                        $column = isset($tableParts[1]) ? $tableParts[1] : $field;
                        
                        $existing = fetchRow("SELECT id FROM {$table} WHERE {$column} = ?", [$value]);
                        if ($existing) {
                            $errors[$field][] = "This {$field} is already taken";
                        }
                    }
                    break;
                    
                case 'exists':
                    if (!empty($value)) {
                        $tableParts = explode('.', $ruleValue);
                        $table = $tableParts[0];
                        $column = isset($tableParts[1]) ? $tableParts[1] : 'id';
                        
                        $existing = fetchRow("SELECT id FROM {$table} WHERE {$column} = ?", [$value]);
                        if (!$existing) {
                            $errors[$field][] = "Selected {$field} does not exist";
                        }
                    }
                    break;
            }
        }
    }
    
    return $errors;
}

/**
 * Hash password securely
 */
function hashPassword($password) {
    $salt = defined('PASSWORD_SALT') ? PASSWORD_SALT : 'default_salt_change_this';
    return password_hash($password . $salt, PASSWORD_DEFAULT);
}

/**
 * Verify password
 */
function verifyPassword($password, $hash) {
    $salt = defined('PASSWORD_SALT') ? PASSWORD_SALT : 'default_salt_change_this';
    return password_verify($password . $salt, $hash);
}

/**
 * Generate secure token
 */
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * Rate limiting
 */
function checkRateLimit($identifier, $maxAttempts = 5, $timeWindow = 900) {
    $cacheKey = "rate_limit_{$identifier}";
    
    // For simplicity, using session storage. In production, use Redis or database
    if (!isset($_SESSION['rate_limits'])) {
        $_SESSION['rate_limits'] = [];
    }
    
    $now = time();
    $attempts = isset($_SESSION['rate_limits'][$cacheKey]) ? $_SESSION['rate_limits'][$cacheKey] : [];
    
    // Remove old attempts
    $attempts = array_filter($attempts, function($timestamp) use ($now, $timeWindow) {
        return ($now - $timestamp) < $timeWindow;
    });
    
    if (count($attempts) >= $maxAttempts) {
        return false;
    }
    
    // Add current attempt
    $attempts[] = $now;
    $_SESSION['rate_limits'][$cacheKey] = $attempts;
    
    return true;
}

/**
 * Check for SQL injection patterns
 */
function detectSQLInjection($input) {
    $patterns = [
        '/(\bunion\b.*\bselect\b)/i',
        '/(\bselect\b.*\bfrom\b)/i',
        '/(\binsert\b.*\binto\b)/i',
        '/(\bupdate\b.*\bset\b)/i',
        '/(\bdelete\b.*\bfrom\b)/i',
        '/(\bdrop\b.*\btable\b)/i',
        '/(\btruncate\b.*\btable\b)/i',
        '/(\balter\b.*\btable\b)/i',
        '/(\bcreate\b.*\btable\b)/i',
        '/(\bexec\b|\bexecute\b)/i',
        '/(\bsp_\w+)/i',
        '/(\bxp_\w+)/i',
        '/(\'|\"|;|--|\#|\*|\bor\b|\band\b)/i'
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $input)) {
            return true;
        }
    }
    
    return false;
}

/**
 * Check for XSS patterns
 */
function detectXSS($input) {
    $patterns = [
        '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi',
        '/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/mi',
        '/javascript:/i',
        '/vbscript:/i',
        '/onload\s*=/i',
        '/onerror\s*=/i',
        '/onclick\s*=/i',
        '/onmouseover\s*=/i',
        '/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/mi',
        '/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/mi'
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $input)) {
            return true;
        }
    }
    
    return false;
}

/**
 * Security middleware
 */
function securityMiddleware() {
    // Check for malicious requests
    $requestData = array_merge($_GET, $_POST, $_COOKIE);
    
    foreach ($requestData as $key => $value) {
        if (is_string($value)) {
            if (detectSQLInjection($value) || detectXSS($value)) {
                logSecurityIncident('malicious_request', [
                    'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                    'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
                    'data' => $key . '=' . substr($value, 0, 100)
                ]);
                
                http_response_code(403);
                die('Forbidden: Malicious request detected');
            }
        }
    }
    
    // Check request method
    $allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'];
    $requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';
    
    if (!in_array($requestMethod, $allowedMethods)) {
        http_response_code(405);
        die('Method Not Allowed');
    }
    
    // Check content length
    $maxContentLength = 10 * 1024 * 1024; // 10MB
    $contentLength = $_SERVER['CONTENT_LENGTH'] ?? 0;
    
    if ($contentLength > $maxContentLength) {
        http_response_code(413);
        die('Request Entity Too Large');
    }
}

/**
 * Log security incidents
 */
function logSecurityIncident($type, $data) {
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'type' => $type,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'data' => $data
    ];
    
    error_log("SECURITY INCIDENT: " . json_encode($logEntry));
    
    // In production, you might want to send alerts or store in database
}

/**
 * IP whitelist check (for admin areas)
 */
function checkIPWhitelist($allowedIPs = []) {
    if (empty($allowedIPs)) {
        return true; // No restriction if no IPs specified
    }
    
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
    
    // Check for forwarded IP (if behind proxy)
    if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $forwardedIPs = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        $clientIP = trim($forwardedIPs[0]);
    }
    
    return in_array($clientIP, $allowedIPs);
}

/**
 * CSRF token functions are defined in config/config.php
 * to avoid redeclaration errors
 */

/**
 * Secure file upload
 */
function secureFileUpload($file, $allowedTypes = [], $maxSize = 5242880) {
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        return ['success' => false, 'message' => 'Invalid file upload'];
    }
    
    // Check file size
    if ($file['size'] > $maxSize) {
        return ['success' => false, 'message' => 'File too large'];
    }
    
    // Check file type
    $fileInfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($fileInfo, $file['tmp_name']);
    finfo_close($fileInfo);
    
    if (!empty($allowedTypes) && !in_array($mimeType, $allowedTypes)) {
        return ['success' => false, 'message' => 'File type not allowed'];
    }
    
    // Generate secure filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = bin2hex(random_bytes(16)) . '.' . $extension;
    
    return ['success' => true, 'filename' => $filename, 'mime_type' => $mimeType];
}

/**
 * Initialize security measures
 */
function initSecurity() {
    // Set security headers
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');
    
    // Run security middleware
    securityMiddleware();
}

// Initialize security on every request
initSecurity();
?>
