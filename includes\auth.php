<?php
/**
 * AstroGenix Authentication System
 * User registration, login, and session management
 */

// Prevent direct access
if (!defined('ROOT_PATH')) {
    die('Direct access not allowed');
}

/**
 * Register new user
 */
function registerUser($data) {
    try {
        // Validate input
        $rules = [
            'username' => 'required|min:3|max:20|username|unique:users.username',
            'email' => 'required|email|unique:users.email',
            'password' => 'required|password',
            'first_name' => 'required|min:2|max:50|alpha',
            'last_name' => 'required|min:2|max:50|alpha',
            'phone' => 'max:20',
            'country' => 'max:50'
        ];
        
        $errors = validateInput($data, $rules);
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        // Check if referral code exists
        $referrerId = null;
        if (!empty($data['referral_code'])) {
            $referrer = fetchRow("SELECT id FROM users WHERE referral_code = ?", [$data['referral_code']]);
            if ($referrer) {
                $referrerId = $referrer['id'];
            }
        }
        
        beginTransaction();
        
        // Generate unique referral code for new user
        $userReferralCode = generateReferralCode();
        
        // Hash password
        $hashedPassword = hashPassword($data['password']);
        
        // Insert user
        $sql = "INSERT INTO users (username, email, password, first_name, last_name, phone, country, referral_code, referred_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $userId = insertAndGetId($sql, [
            sanitizeInput($data['username'], 'username'),
            sanitizeInput($data['email'], 'email'),
            $hashedPassword,
            sanitizeInput($data['first_name']),
            sanitizeInput($data['last_name']),
            sanitizeInput($data['phone'], 'phone'),
            sanitizeInput($data['country']),
            $userReferralCode,
            $referrerId
        ]);
        
        if (!$userId) {
            rollback();
            return ['success' => false, 'message' => 'Registration failed'];
        }
        
        // Add registration bonus
        $registrationBonus = getSetting('registration_bonus', 10);
        if ($registrationBonus > 0) {
            updateUserBalance($userId, $registrationBonus, 'add');
            createTransaction($userId, 'bonus', $registrationBonus, 'Registration bonus');
        }
        
        // Process referral bonus if referred
        if ($referrerId) {
            processReferralCommissions($userId, $registrationBonus, 'deposit');
        }
        
        commit();
        
        // Log activity
        logActivity($userId, 'user_registered', "New user registration: {$data['username']}");
        
        return ['success' => true, 'user_id' => $userId];
        
    } catch (Exception $e) {
        rollback();
        error_log("Registration failed: " . $e->getMessage());
        return ['success' => false, 'message' => 'Registration failed. Please try again.'];
    }
}

/**
 * Login user
 */
function loginUser($username, $password, $rememberMe = false) {
    try {
        // Rate limiting
        $identifier = $_SERVER['REMOTE_ADDR'] . '_' . $username;
        if (!checkRateLimit($identifier, MAX_LOGIN_ATTEMPTS, LOGIN_LOCKOUT_TIME)) {
            return ['success' => false, 'message' => 'Too many login attempts. Please try again later.'];
        }
        
        // Find user by username or email
        $sql = "SELECT * FROM users WHERE (username = ? OR email = ?) AND is_active = 1";
        $user = fetchRow($sql, [$username, $username]);
        
        if (!$user || !verifyPassword($password, $user['password'])) {
            logSecurityIncident('failed_login', [
                'username' => $username,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? ''
            ]);
            return ['success' => false, 'message' => 'Invalid credentials'];
        }
        
        // Check if email verification is required
        if (getSetting('email_verification', true) && !$user['is_verified']) {
            return ['success' => false, 'message' => 'Please verify your email address first'];
        }
        
        // Create session
        session_regenerate_id(true);
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['logged_in'] = true;
        $_SESSION['login_time'] = time();
        
        // Handle remember me
        if ($rememberMe) {
            $token = generateSecureToken();
            $expiry = time() + (30 * 24 * 60 * 60); // 30 days
            
            // Store token in database (you might want to create a remember_tokens table)
            setcookie('remember_token', $token, $expiry, '/', '', false, true);
        }
        
        // Update last login
        executeQuery("UPDATE users SET last_login = NOW() WHERE id = ?", [$user['id']]);
        
        // Log activity
        logActivity($user['id'], 'user_login', "User logged in from IP: " . ($_SERVER['REMOTE_ADDR'] ?? ''));
        
        return ['success' => true, 'user' => $user];
        
    } catch (Exception $e) {
        error_log("Login failed: " . $e->getMessage());
        return ['success' => false, 'message' => 'Login failed. Please try again.'];
    }
}

/**
 * Logout user
 */
function logoutUser() {
    if (isset($_SESSION['user_id'])) {
        logActivity($_SESSION['user_id'], 'user_logout', 'User logged out');
    }
    
    // Clear session
    session_unset();
    session_destroy();
    
    // Clear remember me cookie
    if (isset($_COOKIE['remember_token'])) {
        setcookie('remember_token', '', time() - 3600, '/', '', false, true);
    }
    
    // Start new session
    session_start();
    session_regenerate_id(true);
}

/**
 * Check if user is authenticated
 */
function requireAuth() {
    if (!isLoggedIn()) {
        if (isAjaxRequest()) {
            jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
        } else {
            redirect('/login.php');
        }
    }
}

/**
 * Check if admin is authenticated
 */
function requireAdminAuth() {
    if (!isAdmin()) {
        if (isAjaxRequest()) {
            jsonResponse(['success' => false, 'message' => 'Admin authentication required'], 401);
        } else {
            redirect('/admin/login.php');
        }
    }
}

/**
 * Admin login
 */
function loginAdmin($username, $password) {
    try {
        // Rate limiting
        $identifier = 'admin_' . $_SERVER['REMOTE_ADDR'] . '_' . $username;
        if (!checkRateLimit($identifier, MAX_LOGIN_ATTEMPTS, LOGIN_LOCKOUT_TIME)) {
            return ['success' => false, 'message' => 'Too many login attempts. Please try again later.'];
        }
        
        // Find admin user
        $sql = "SELECT * FROM admin_users WHERE (username = ? OR email = ?) AND is_active = 1";
        $admin = fetchRow($sql, [$username, $username]);
        
        if (!$admin || !verifyPassword($password, $admin['password'])) {
            logSecurityIncident('failed_admin_login', [
                'username' => $username,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? ''
            ]);
            return ['success' => false, 'message' => 'Invalid credentials'];
        }
        
        // Create admin session
        session_regenerate_id(true);
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['admin_username'] = $admin['username'];
        $_SESSION['admin_role'] = $admin['role'];
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_login_time'] = time();
        
        // Update last login
        executeQuery("UPDATE admin_users SET last_login = NOW() WHERE id = ?", [$admin['id']]);
        
        // Log activity
        error_log("Admin login: {$admin['username']} from IP: " . ($_SERVER['REMOTE_ADDR'] ?? ''));
        
        return ['success' => true, 'admin' => $admin];
        
    } catch (Exception $e) {
        error_log("Admin login failed: " . $e->getMessage());
        return ['success' => false, 'message' => 'Login failed. Please try again.'];
    }
}

/**
 * Logout admin
 */
function logoutAdmin() {
    if (isset($_SESSION['admin_id'])) {
        error_log("Admin logout: {$_SESSION['admin_username']}");
    }
    
    // Clear admin session variables
    unset($_SESSION['admin_id']);
    unset($_SESSION['admin_username']);
    unset($_SESSION['admin_role']);
    unset($_SESSION['admin_logged_in']);
    unset($_SESSION['admin_login_time']);
    
    session_regenerate_id(true);
}

/**
 * Change user password
 */
function changePassword($userId, $currentPassword, $newPassword) {
    try {
        // Get current user
        $user = getUserById($userId);
        if (!$user) {
            return ['success' => false, 'message' => 'User not found'];
        }
        
        // Verify current password
        if (!verifyPassword($currentPassword, $user['password'])) {
            return ['success' => false, 'message' => 'Current password is incorrect'];
        }
        
        // Validate new password
        $errors = validateInput(['password' => $newPassword], ['password' => 'required|password']);
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        // Hash new password
        $hashedPassword = hashPassword($newPassword);
        
        // Update password
        $sql = "UPDATE users SET password = ? WHERE id = ?";
        $result = executeQuery($sql, [$hashedPassword, $userId]);
        
        if ($result) {
            logActivity($userId, 'password_changed', 'User changed password');
            return ['success' => true, 'message' => 'Password changed successfully'];
        } else {
            return ['success' => false, 'message' => 'Failed to change password'];
        }
        
    } catch (Exception $e) {
        error_log("Password change failed: " . $e->getMessage());
        return ['success' => false, 'message' => 'Password change failed. Please try again.'];
    }
}

/**
 * Reset password (placeholder for future implementation)
 */
function resetPassword($email) {
    // This would typically send a reset email
    // For now, just log the request
    error_log("Password reset requested for: {$email}");
    return ['success' => true, 'message' => 'Password reset instructions sent to your email'];
}

/**
 * Check if request is AJAX
 */
function isAjaxRequest() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Session timeout check
 */
function checkSessionTimeout($timeout = 3600) {
    if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time']) > $timeout) {
        logoutUser();
        return false;
    }
    
    // Update last activity time
    $_SESSION['login_time'] = time();
    return true;
}

/**
 * Get current user data
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    return getUserById($_SESSION['user_id']);
}

/**
 * Update user profile
 */
function updateUserProfile($userId, $data) {
    try {
        // Validate input
        $rules = [
            'first_name' => 'required|min:2|max:50|alpha',
            'last_name' => 'required|min:2|max:50|alpha',
            'phone' => 'max:20',
            'country' => 'max:50'
        ];
        
        $errors = validateInput($data, $rules);
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        // Update user
        $sql = "UPDATE users SET first_name = ?, last_name = ?, phone = ?, country = ? WHERE id = ?";
        $result = executeQuery($sql, [
            sanitizeInput($data['first_name']),
            sanitizeInput($data['last_name']),
            sanitizeInput($data['phone'], 'phone'),
            sanitizeInput($data['country']),
            $userId
        ]);
        
        if ($result) {
            logActivity($userId, 'profile_updated', 'User updated profile information');
            return ['success' => true, 'message' => 'Profile updated successfully'];
        } else {
            return ['success' => false, 'message' => 'Failed to update profile'];
        }
        
    } catch (Exception $e) {
        error_log("Profile update failed: " . $e->getMessage());
        return ['success' => false, 'message' => 'Profile update failed. Please try again.'];
    }
}
?>
