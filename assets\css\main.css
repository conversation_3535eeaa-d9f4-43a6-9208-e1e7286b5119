/* AstroGenix - Modern Crypto Exchange Style CSS */

/* CSS Variables - Poseidon Style Design */
:root {
    /* Primary Colors - Poseidon Blue Palette */
    --primary-color: #2196F3;
    --primary-dark: #1976D2;
    --primary-light: #42A5F5;
    --primary-ultra-light: #90CAF9;

    /* Background Colors - Poseidon Style */
    --bg-primary: #1A237E;
    --bg-secondary: #283593;
    --bg-tertiary: #3949AB;
    --bg-gradient: linear-gradient(135deg, #1A237E 0%, #3949AB 100%);
    --bg-card: #FFFFFF;
    --bg-section-light: #F8F9FA;

    /* Text Colors */
    --text-primary: #FFFFFF;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --text-muted: rgba(255, 255, 255, 0.6);
    --text-dark: #2C3E50;
    --text-dark-secondary: #5A6C7D;
    --text-accent: #2196F3;

    /* Accent Colors */
    --accent-blue: #2196F3;
    --accent-green: #4CAF50;
    --accent-orange: #FF9800;
    --accent-purple: #9C27B0;
    --accent-cyan: #00BCD4;
    --success-color: #4CAF50;
    --warning-color: #FF9800;
    --error-color: #F44336;

    /* Border & Divider Colors */
    --border-color: rgba(255, 255, 255, 0.1);
    --border-light: rgba(255, 255, 255, 0.2);
    --border-card: #E0E0E0;
    --divider-color: #E0E0E0;

    /* Shadows - Poseidon Style */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.18);
    --shadow-2xl: 0 16px 32px rgba(0, 0, 0, 0.2);
    --shadow-card: 0 4px 20px rgba(0, 0, 0, 0.08);

    /* Gradients - Poseidon Style */
    --gradient-primary: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    --gradient-secondary: linear-gradient(135deg, #1A237E 0%, #3949AB 100%);
    --gradient-accent: linear-gradient(135deg, #00BCD4 0%, #0097A7 100%);
    --gradient-success: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
    --gradient-warning: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
    --gradient-purple: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);

    /* Animation & Transition Variables */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;

    /* Border Radius - Poseidon Style */
    --radius-sm: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.25rem;
    --radius-2xl: 1.5rem;
}

/* Reset and base styles - Modern Crypto Exchange */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: var(--bg-gradient);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    font-variant-numeric: tabular-nums;
    position: relative;
}

/* Poseidon Style Background Elements */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 20%, rgba(33, 150, 243, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(156, 39, 176, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(0, 188, 212, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Modern Typography - Crypto Exchange Style */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
    letter-spacing: -0.025em;
    color: var(--text-primary);
}

h1 {
    font-size: 2.5rem;
    font-weight: 700;
    letter-spacing: -0.05em;
}
h2 {
    font-size: 2rem;
    font-weight: 600;
    letter-spacing: -0.04em;
}
h3 {
    font-size: 1.75rem;
    font-weight: 600;
    letter-spacing: -0.03em;
}
h4 {
    font-size: 1.5rem;
    font-weight: 600;
    letter-spacing: -0.02em;
}
h5 {
    font-size: 1.25rem;
    font-weight: 500;
}
h6 {
    font-size: 1rem;
    font-weight: 500;
}

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
    line-height: 1.7;
}

a {
    color: var(--primary-light);
    text-decoration: none;
    transition: all var(--transition-normal);
    position: relative;
}

a:hover {
    color: var(--primary-ultra-light);
    transform: translateY(-1px);
}

a:focus {
    outline: 2px solid var(--primary-light);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
}

/* Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.5rem;
}

.col {
    flex: 1;
    padding: 0 0.5rem;
}

.col-1 { flex: 0 0 8.333333%; }
.col-2 { flex: 0 0 16.666667%; }
.col-3 { flex: 0 0 25%; }
.col-4 { flex: 0 0 33.333333%; }
.col-6 { flex: 0 0 50%; }
.col-8 { flex: 0 0 66.666667%; }
.col-9 { flex: 0 0 75%; }
.col-12 { flex: 0 0 100%; }

/* Header */
./* Header - Poseidon Style */
header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-sm);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.logo:hover {
    color: var(--primary-light);
    transform: translateY(-1px);
}

.logo::before {
    content: '⭐';
    width: 40px;
    height: 40px;
    margin-right: 0.75rem;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    box-shadow: var(--shadow-md);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 1.5rem;
    margin: 0;
    padding: 0;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    padding: 0.75rem 1.25rem;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    position: relative;
    font-size: 0.95rem;
}

.nav-link:hover,
.nav-link.active {
    color: var(--text-primary);
    background: rgba(33, 150, 243, 0.15);
    transform: translateY(-1px);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Modern Buttons - Crypto Exchange Style */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    user-select: none;
    font-family: inherit;
    line-height: 1.5;
    min-height: 44px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.3);
}

/* Primary Button - Poseidon Style */
.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-primary);
    box-shadow: var(--shadow-md);
    border: 1px solid transparent;
    font-weight: 600;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, #1976D2 0%, #1565C0 100%);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

/* Secondary Button */
.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--bg-card);
    border-color: var(--border-light);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Outline Button */
.btn-outline {
    background: transparent;
    border: 1px solid var(--primary-light);
    color: var(--primary-light);
    backdrop-filter: blur(10px);
}

.btn-outline:hover:not(:disabled) {
    background: rgba(79, 70, 229, 0.1);
    border-color: var(--primary-ultra-light);
    color: var(--primary-ultra-light);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Status Buttons */
.btn-success {
    background: var(--success-color);
    color: var(--text-primary);
    box-shadow: var(--shadow-sm);
}

.btn-success:hover:not(:disabled) {
    background: #059669;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-warning {
    background: var(--warning-color);
    color: var(--text-primary);
    box-shadow: var(--shadow-sm);
}

.btn-warning:hover:not(:disabled) {
    background: #D97706;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-error {
    background: var(--error-color);
    color: var(--text-primary);
    box-shadow: var(--shadow-sm);
}

.btn-error:hover:not(:disabled) {
    background: #DC2626;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Button Sizes */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    min-height: 36px;
    border-radius: var(--radius-md);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1rem;
    min-height: 52px;
    border-radius: var(--radius-xl);
}

.btn-xl {
    padding: 1.25rem 2.5rem;
    font-size: 1.125rem;
    min-height: 60px;
    border-radius: var(--radius-xl);
}

/* Full Width Button */
.btn-full,
.w-full {
    width: 100%;
}

/* Button with Icon */
.btn-icon {
    padding: 0.75rem;
    min-width: 44px;
    aspect-ratio: 1;
}

/* Loading State */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    color: var(--text-primary);
}

/* Cards - Poseidon Style */
.card {
    background: var(--bg-card);
    border: 1px solid var(--border-card);
    border-radius: var(--radius-xl);
    padding: 2rem;
    box-shadow: var(--shadow-card);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: rgba(33, 150, 243, 0.2);
}

/* Card Variants */
.card-glass {
    background: rgba(31, 41, 55, 0.6);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(79, 70, 229, 0.2);
}

.card-primary {
    background: var(--gradient-primary);
    border: 1px solid rgba(79, 70, 229, 0.3);
    color: var(--text-primary);
}

.card-accent {
    border-left: 4px solid var(--primary-light);
}

.card-featured {
    position: relative;
    border: 2px solid var(--primary-light);
    box-shadow: var(--shadow-xl), var(--shadow-glow);
}

.card-featured::after {
    content: 'FEATURED';
    position: absolute;
    top: -1px;
    right: 1rem;
    background: var(--gradient-primary);
    color: var(--text-primary);
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 0 0 var(--radius-md) var(--radius-md);
    letter-spacing: 0.05em;
}

.card-header {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.card-subtitle {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.card-body {
    margin-bottom: 1rem;
}

.card-footer {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* Modern Forms - Crypto Exchange Style */
.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
    letter-spacing: 0.025em;
}

.form-control {
    width: 100%;
    padding: 0.875rem 1rem;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: 0.875rem;
    font-family: inherit;
    transition: all var(--transition-normal);
    min-height: 48px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-light);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    background: rgba(31, 41, 55, 0.8);
    transform: translateY(-1px);
}

.form-control:hover:not(:focus) {
    border-color: var(--border-light);
}

.form-control::placeholder {
    color: var(--text-muted);
    opacity: 0.7;
}

.form-control:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: var(--bg-secondary);
}

/* Input with Icon */
.form-input-icon {
    position: relative;
}

.form-input-icon .form-control {
    padding-left: 2.5rem;
}

.form-input-icon .form-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: 1rem;
    pointer-events: none;
}

/* Floating Label Style */
.form-floating {
    position: relative;
}

.form-floating .form-control {
    padding: 1rem 1rem 0.5rem;
}

.form-floating .form-label {
    position: absolute;
    top: 0;
    left: 1rem;
    height: 100%;
    padding: 1rem 0;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 0 0;
    transition: all var(--transition-normal);
    margin-bottom: 0;
}

.form-floating .form-control:focus ~ .form-label,
.form-floating .form-control:not(:placeholder-shown) ~ .form-label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* Form Validation States */
.form-control.is-valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-control.is-invalid {
    border-color: var(--error-color);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-feedback {
    margin-top: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.form-feedback.valid-feedback {
    color: var(--success-color);
}

.form-feedback.invalid-feedback {
    color: var(--error-color);
}

/* Select Dropdown */
.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    appearance: none;
}

/* Checkbox and Radio */
.form-check {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.form-check-input {
    width: 1.125rem;
    height: 1.125rem;
    margin: 0;
    accent-color: var(--primary-light);
    border-radius: var(--radius-sm);
}

.form-check-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    cursor: pointer;
    user-select: none;
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background: var(--bg-tertiary);
    font-weight: 600;
    color: var(--text-primary);
}

.table tbody tr:hover {
    background: var(--bg-tertiary);
}

/* Status badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.badge-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

.badge-error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
    border: 1px solid var(--error-color);
}

.badge-info {
    background: rgba(6, 182, 212, 0.1);
    color: var(--accent-color);
    border: 1px solid var(--accent-color);
}

/* Alerts */
.alert {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    border-color: var(--warning-color);
    color: var(--warning-color);
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--error-color);
    color: var(--error-color);
}

.alert-info {
    background: rgba(6, 182, 212, 0.1);
    border-color: var(--accent-color);
    color: var(--accent-color);
}

/* Modern Animations - Crypto Exchange Style */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(79, 70, 229, 0.3);
    }
    50% {
        box-shadow: 0 0 30px rgba(79, 70, 229, 0.6);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.8s ease-out forwards;
}

.fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
}

.slide-in {
    animation: slideIn 0.6s ease-out forwards;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out forwards;
}

.pulse {
    animation: pulse 3s ease-in-out infinite;
}

.glow {
    animation: glow 2s ease-in-out infinite;
}

.float {
    animation: float 3s ease-in-out infinite;
}

/* Staggered Animations */
.fade-in-delay-1 { animation-delay: 0.1s; }
.fade-in-delay-2 { animation-delay: 0.2s; }
.fade-in-delay-3 { animation-delay: 0.3s; }
.fade-in-delay-4 { animation-delay: 0.4s; }
.fade-in-delay-5 { animation-delay: 0.5s; }

/* Hover Animations */
.hover-lift {
    transition: transform var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-4px);
}

.hover-scale {
    transition: transform var(--transition-normal);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: box-shadow var(--transition-normal);
}

.hover-glow:hover {
    box-shadow: var(--shadow-glow);
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.rounded { border-radius: 0.5rem; }
.rounded-lg { border-radius: 1rem; }
.rounded-full { border-radius: 9999px; }

.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Loading spinner */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-light);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes - Modern Crypto Exchange */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-flex { display: flex; }
.d-block { display: block; }
.d-inline-block { display: inline-block; }
.d-none { display: none; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }

.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }

.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-5 { gap: 1.25rem; }
.gap-6 { gap: 1.5rem; }

.m-0 { margin: 0; }
.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 0.75rem; }
.m-4 { margin: 1rem; }
.m-5 { margin: 1.25rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-5 { padding: 1.25rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-5 { margin-top: 1.25rem; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-5 { margin-bottom: 1.25rem; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 0.25rem; }
.pt-2 { padding-top: 0.5rem; }
.pt-3 { padding-top: 0.75rem; }
.pt-4 { padding-top: 1rem; }
.pt-5 { padding-top: 1.25rem; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 0.25rem; }
.pb-2 { padding-bottom: 0.5rem; }
.pb-3 { padding-bottom: 0.75rem; }
.pb-4 { padding-bottom: 1rem; }
.pb-5 { padding-bottom: 1.25rem; }

.w-full { width: 100%; }
.w-auto { width: auto; }
.h-full { height: 100%; }
.h-auto { height: auto; }

.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }

.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

.select-none { user-select: none; }

.rounded { border-radius: var(--radius-md); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: 9999px; }

.shadow { box-shadow: var(--shadow-md); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }
.shadow-none { box-shadow: none; }

.border { border: 1px solid var(--border-color); }
.border-0 { border: none; }
.border-t { border-top: 1px solid var(--border-color); }
.border-b { border-bottom: 1px solid var(--border-color); }
.border-l { border-left: 1px solid var(--border-color); }
.border-r { border-right: 1px solid var(--border-color); }

.bg-primary { background: var(--bg-primary); }
.bg-secondary { background: var(--bg-secondary); }
.bg-tertiary { background: var(--bg-tertiary); }
.bg-transparent { background: transparent; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-accent { color: var(--text-accent); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }

/* Modern Hero Section - Clean Crypto Exchange Style */
.hero {
    padding: 8rem 0 6rem;
    background: var(--bg-gradient);
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at top, rgba(79, 70, 229, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.hero::after {
    content: '';
    position: absolute;
    top: 20%;
    right: -10%;
    width: 40%;
    height: 60%;
    background: radial-gradient(circle, rgba(79, 70, 229, 0.05) 0%, transparent 70%);
    border-radius: 50%;
    filter: blur(40px);
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    max-width: 500px;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-light);
    display: block;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.hero-actions {
    display: flex;
    gap: 1rem;
}

/* Poseidon Style Hero Elements */
.hero-text {
    text-align: left;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(33, 150, 243, 0.1);
    color: var(--accent-blue);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-xl);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(33, 150, 243, 0.2);
}

.hero-title .highlight {
    color: var(--accent-blue);
}

.hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-card {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
    max-width: 400px;
    width: 100%;
}

.hero-card-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--gradient-success);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.hero-card-image {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
    border-radius: var(--radius-lg);
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
}

.hero-card-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.hero-card-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.hero-card-info h4 {
    color: var(--text-dark);
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    font-weight: 600;
}

.hero-card-info p {
    color: var(--text-dark-secondary);
    margin: 0;
    font-size: 0.875rem;
}

/* Poseidon Style Sections */
.section {
    padding: 6rem 0;
}

.section-light {
    background: var(--bg-section-light);
    color: var(--text-dark);
}

.section-dark {
    background: var(--bg-gradient);
    color: var(--text-primary);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--gradient-primary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-xl);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-dark-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.section-light .section-subtitle {
    color: var(--text-dark-secondary);
}

.section-dark .section-subtitle {
    color: var(--text-secondary);
}

/* Features Grid - Poseidon Style */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-card);
    transition: all var(--transition-normal);
    border: 1px solid var(--border-card);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 1.5rem;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    font-weight: bold;
}

.feature-icon.blue {
    background: var(--gradient-primary);
}

.feature-icon.green {
    background: var(--gradient-success);
}

.feature-icon.orange {
    background: var(--gradient-warning);
}

.feature-icon.purple {
    background: var(--gradient-purple);
}

.feature-icon.cyan {
    background: var(--gradient-accent);
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.feature-description {
    color: var(--text-dark-secondary);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.feature-link {
    color: var(--accent-blue);
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    transition: all var(--transition-fast);
}

.feature-link:hover {
    color: var(--primary-dark);
    transform: translateX(2px);
}

/* Newsletter Section Styles */
.newsletter-section {
    background: rgba(33, 150, 243, 0.1);
    border-radius: var(--radius-xl);
    padding: 3rem;
    text-align: center;
    border: 1px solid rgba(33, 150, 243, 0.2);
}

.newsletter-form {
    display: flex;
    gap: 1rem;
    max-width: 400px;
    margin: 0 auto;
    align-items: center;
}

.newsletter-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.newsletter-input::placeholder {
    color: var(--text-secondary);
}

/* Footer Styles - Poseidon */
.footer-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-brand {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.footer-brand-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin-right: 0.75rem;
}

.footer-brand-name {
    color: var(--text-primary);
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.footer-description {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.footer-badges {
    display: flex;
    gap: 1rem;
}

.footer-badge {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem;
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.footer-section h4 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--text-primary);
}

.footer-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
    text-align: center;
}

.footer-stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.footer-stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.footer-bottom {
    border-top: 1px solid var(--border-color);
    padding-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-bottom p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.875rem;
}

.footer-bottom-links {
    display: flex;
    gap: 2rem;
}

.footer-bottom-links a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color var(--transition-fast);
}

.footer-bottom-links a:hover {
    color: var(--text-primary);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1rem;
}

.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
}

.crypto-animation {
    width: 320px;
    height: 320px;
    background: var(--gradient-primary);
    border-radius: 50%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-2xl), var(--shadow-glow);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.crypto-animation::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: linear-gradient(45deg, rgba(79, 70, 229, 0.3), rgba(99, 102, 241, 0.1));
    border-radius: 50%;
    animation: pulse 4s ease-in-out infinite;
    z-index: -1;
}

.crypto-icon {
    font-size: 4rem;
    color: var(--text-primary);
    font-weight: bold;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    animation: float 3s ease-in-out infinite;
}

/* Sections */
.packages-section,
.features-section,
.news-section {
    padding: 5rem 0;
}

.section-header {
    margin-bottom: 3rem;
}

.section-header h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.125rem;
    color: var(--text-secondary);
}

/* Package Cards */
.packages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.package-card {
    position: relative;
    background: var(--gradient-secondary);
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
}

.package-card:hover {
    border-color: var(--primary-light);
    transform: translateY(-10px);
}

.package-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat {
    text-align: center;
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: 0.5rem;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-light);
}

.package-range {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.range-label {
    color: var(--text-secondary);
}

.range-value {
    font-weight: 600;
    color: var(--text-primary);
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    text-align: center;
    padding: 2rem;
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.feature-card h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
}

/* News Grid */
.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.news-card {
    overflow: hidden;
}

.news-image {
    height: 200px;
    overflow: hidden;
    margin: -1.5rem -1.5rem 1rem -1.5rem;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-card:hover .news-image img {
    transform: scale(1.05);
}

.news-content h3 {
    margin-bottom: 0.5rem;
    font-size: 1.125rem;
}

.news-content p {
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.news-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Footer */
.footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: 3rem 0 1rem;
    margin-top: 5rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--text-secondary);
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-light);
}

.social-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.social-link {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.social-link:hover {
    color: var(--primary-light);
}

.footer-bottom {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.footer-bottom p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.875rem;
}

/* Navigation improvements */
.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.language-switcher select {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.5rem;
    border-radius: 0.25rem;
}

/* Notifications */
.notifications-container {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 10000;
    max-width: 400px;
}

.notification {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    box-shadow: var(--shadow-lg);
    transform: translateX(100%);
    transition: all 0.3s ease;
    opacity: 0;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
}

.notification-message {
    color: var(--text-primary);
    font-size: 0.875rem;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0;
    margin-left: 1rem;
}

.notification-close:hover {
    color: var(--text-primary);
}

.notification-success {
    border-left: 4px solid var(--success-color);
}

.notification-error {
    border-left: 4px solid var(--error-color);
}

.notification-warning {
    border-left: 4px solid var(--warning-color);
}

.notification-info {
    border-left: 4px solid var(--accent-color);
}

/* Tooltips */
.tooltip {
    position: absolute;
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 0.5rem 0.75rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-md);
    z-index: 1000;
    opacity: 0;
    transform: translateY(5px);
    transition: all 0.2s ease;
    pointer-events: none;
}

.tooltip.show {
    opacity: 1;
    transform: translateY(0);
}

.tooltip:before {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--border-color);
}

.tooltip:after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--bg-secondary);
    margin-top: -1px;
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal.active .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
}

.modal-close:hover {
    color: var(--text-primary);
}

.modal-body {
    margin-bottom: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* Loading states */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.loading-spinner .spinner {
    margin-bottom: 1rem;
}

.loading-spinner p {
    margin: 0;
    font-size: 0.875rem;
}

/* Progress bars */
.progress {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-bar-success {
    background: var(--success-color);
}

.progress-bar-warning {
    background: var(--warning-color);
}

.progress-bar-error {
    background: var(--error-color);
}

/* Tabs */
.tabs {
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.tab-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.tab-item {
    margin-right: 2rem;
}

.tab-link {
    display: block;
    padding: 1rem 0;
    color: var(--text-secondary);
    text-decoration: none;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-link:hover,
.tab-link.active {
    color: var(--primary-light);
    border-bottom-color: var(--primary-light);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Responsive Design - Poseidon Style */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }

    .navbar {
        flex-direction: column;
        gap: 1rem;
        padding: 0 1rem;
    }

    .nav-menu {
        flex-direction: column;
        width: 100%;
        text-align: center;
        gap: 0.5rem;
    }

    .nav-actions {
        flex-direction: column;
        width: 100%;
        gap: 0.5rem;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-text {
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        justify-content: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .footer-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .footer-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .footer-bottom-links {
        justify-content: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .newsletter-form {
        flex-direction: column;
        max-width: 100%;
    }

    .newsletter-input {
        width: 100%;
    }

    .packages-grid {
        grid-template-columns: 1fr;
    }

    .package-stats {
        grid-template-columns: 1fr;
    }

    .notifications-container {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        max-width: none;
    }

    .modal-content {
        margin: 1rem;
        width: calc(100% - 2rem);
        padding: 1.5rem;
    }

    .tab-list {
        flex-wrap: wrap;
    }

    .tab-item {
        margin-right: 1rem;
        margin-bottom: 0.5rem;
    }

    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-card {
        max-width: 100%;
    }

    .footer-stats {
        grid-template-columns: 1fr;
    }

    .hero-stats {
        flex-direction: column;
        align-items: center;
    }
}
