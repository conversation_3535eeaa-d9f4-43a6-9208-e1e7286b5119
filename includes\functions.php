<?php
/**
 * AstroGenix Core Functions
 * Essential utility functions for the platform
 */

// Prevent direct access
if (!defined('ROOT_PATH')) {
    die('Direct access not allowed');
}

/**
 * Format currency amount
 */
function formatCurrency($amount, $decimals = 6) {
    return number_format((float)$amount, $decimals, '.', ',');
}

/**
 * Format percentage
 */
function formatPercentage($percentage, $decimals = 2) {
    return number_format((float)$percentage, $decimals) . '%';
}

/**
 * Generate random string
 */
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

/**
 * Generate unique referral code
 */
function generateReferralCode($length = 8) {
    do {
        $code = strtoupper(generateRandomString($length));
        $exists = fetchRow("SELECT id FROM users WHERE referral_code = ?", [$code]);
    } while ($exists);
    
    return $code;
}

/**
 * Calculate investment profit
 */
function calculateInvestmentProfit($amount, $dailyPercentage, $days) {
    $dailyProfit = ($amount * $dailyPercentage) / 100;
    $totalProfit = $dailyProfit * $days;
    return [
        'daily_profit' => $dailyProfit,
        'total_profit' => $totalProfit,
        'total_return' => $amount + $totalProfit
    ];
}

/**
 * Get user by ID
 */
function getUserById($userId) {
    return fetchRow("SELECT * FROM users WHERE id = ?", [$userId]);
}

/**
 * Get user balance
 */
function getUserBalance($userId) {
    $user = getUserById($userId);
    return $user ? (float)$user['balance_usdt'] : 0;
}

/**
 * Update user balance
 */
function updateUserBalance($userId, $amount, $type = 'add') {
    try {
        beginTransaction();
        
        $currentBalance = getUserBalance($userId);
        
        if ($type === 'subtract' && $currentBalance < $amount) {
            rollback();
            return false;
        }
        
        $newBalance = $type === 'add' ? $currentBalance + $amount : $currentBalance - $amount;
        
        $sql = "UPDATE users SET balance_usdt = ? WHERE id = ?";
        $result = executeQuery($sql, [$newBalance, $userId]);
        
        if ($result) {
            commit();
            return true;
        } else {
            rollback();
            return false;
        }
    } catch (Exception $e) {
        rollback();
        error_log("Balance update failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Create transaction record
 */
function createTransaction($userId, $type, $amount, $description, $referenceId = null, $status = 'completed') {
    $sql = "INSERT INTO transactions (user_id, type, amount, description, reference_id, status) 
            VALUES (?, ?, ?, ?, ?, ?)";
    return insertAndGetId($sql, [$userId, $type, $amount, $description, $referenceId, $status]);
}

/**
 * Get user transactions
 */
function getUserTransactions($userId, $limit = 20, $offset = 0) {
    $sql = "SELECT * FROM transactions WHERE user_id = ? 
            ORDER BY created_at DESC LIMIT ? OFFSET ?";
    return fetchAll($sql, [$userId, $limit, $offset]);
}

/**
 * Get investment packages
 */
function getInvestmentPackages($activeOnly = true) {
    $sql = "SELECT * FROM investment_packages";
    $params = [];
    
    if ($activeOnly) {
        $sql .= " WHERE is_active = 1";
    }
    
    $sql .= " ORDER BY sort_order ASC";
    
    return fetchAll($sql, $params);
}

/**
 * Get package by ID
 */
function getPackageById($packageId) {
    return fetchRow("SELECT * FROM investment_packages WHERE id = ?", [$packageId]);
}

/**
 * Create investment
 */
function createInvestment($userId, $packageId, $amount) {
    try {
        beginTransaction();
        
        // Check user balance
        $userBalance = getUserBalance($userId);
        if ($userBalance < $amount) {
            rollback();
            return ['success' => false, 'message' => 'Insufficient balance'];
        }
        
        // Get package details
        $package = getPackageById($packageId);
        if (!$package || !$package['is_active']) {
            rollback();
            return ['success' => false, 'message' => 'Invalid package'];
        }
        
        // Validate amount
        if ($amount < $package['min_amount'] || $amount > $package['max_amount']) {
            rollback();
            return ['success' => false, 'message' => 'Invalid investment amount'];
        }
        
        // Calculate profits
        $profits = calculateInvestmentProfit($amount, $package['daily_percentage'], $package['duration_days']);
        
        // Create investment record
        $sql = "INSERT INTO user_investments (user_id, package_id, amount, daily_profit, total_days, next_payout) 
                VALUES (?, ?, ?, ?, ?, DATE_ADD(CURDATE(), INTERVAL 1 DAY))";
        $investmentId = insertAndGetId($sql, [
            $userId, 
            $packageId, 
            $amount, 
            $profits['daily_profit'], 
            $package['duration_days']
        ]);
        
        if (!$investmentId) {
            rollback();
            return ['success' => false, 'message' => 'Failed to create investment'];
        }
        
        // Deduct amount from user balance
        if (!updateUserBalance($userId, $amount, 'subtract')) {
            rollback();
            return ['success' => false, 'message' => 'Failed to update balance'];
        }
        
        // Update user total invested
        executeQuery("UPDATE users SET total_invested = total_invested + ? WHERE id = ?", [$amount, $userId]);
        
        // Create transaction record
        createTransaction($userId, 'investment', $amount, "Investment in {$package['name']}", $investmentId);
        
        // Process referral commissions
        processReferralCommissions($userId, $amount, 'investment');
        
        commit();
        return ['success' => true, 'investment_id' => $investmentId];
        
    } catch (Exception $e) {
        rollback();
        error_log("Investment creation failed: " . $e->getMessage());
        return ['success' => false, 'message' => 'System error occurred'];
    }
}

/**
 * Get user investments
 */
function getUserInvestments($userId, $status = null) {
    $sql = "SELECT ui.*, ip.name as package_name, ip.daily_percentage 
            FROM user_investments ui 
            JOIN investment_packages ip ON ui.package_id = ip.id 
            WHERE ui.user_id = ?";
    $params = [$userId];
    
    if ($status) {
        $sql .= " AND ui.status = ?";
        $params[] = $status;
    }
    
    $sql .= " ORDER BY ui.created_at DESC";
    
    return fetchAll($sql, $params);
}

/**
 * Process referral commissions
 */
function processReferralCommissions($userId, $amount, $sourceType) {
    try {
        // Get user's referrer
        $user = getUserById($userId);
        if (!$user || !$user['referred_by']) {
            return;
        }
        
        $referrerId = $user['referred_by'];
        $level = 1;
        $maxLevels = REFERRAL_LEVELS;
        
        while ($referrerId && $level <= $maxLevels) {
            // Get commission percentage for this level
            $commissionRate = 0;
            switch ($level) {
                case 1:
                    $commissionRate = REFERRAL_LEVEL_1;
                    break;
                case 2:
                    $commissionRate = REFERRAL_LEVEL_2;
                    break;
                case 3:
                    $commissionRate = REFERRAL_LEVEL_3;
                    break;
            }
            
            if ($commissionRate > 0) {
                $commissionAmount = ($amount * $commissionRate) / 100;
                
                // Add commission to referrer's balance
                updateUserBalance($referrerId, $commissionAmount, 'add');
                
                // Update referrer's total referral earnings
                executeQuery("UPDATE users SET referral_earnings = referral_earnings + ? WHERE id = ?", 
                           [$commissionAmount, $referrerId]);
                
                // Create commission record
                $sql = "INSERT INTO referral_commissions (referrer_id, referred_id, level, amount, percentage, source_type, source_amount) 
                        VALUES (?, ?, ?, ?, ?, ?, ?)";
                executeQuery($sql, [$referrerId, $userId, $level, $commissionAmount, $commissionRate, $sourceType, $amount]);
                
                // Create transaction record
                createTransaction($referrerId, 'referral', $commissionAmount, 
                               "Level {$level} referral commission from user #{$userId}");
            }
            
            // Get next level referrer
            $referrer = getUserById($referrerId);
            $referrerId = $referrer ? $referrer['referred_by'] : null;
            $level++;
        }
    } catch (Exception $e) {
        error_log("Referral commission processing failed: " . $e->getMessage());
    }
}

/**
 * Get user referrals
 */
function getUserReferrals($userId) {
    $sql = "SELECT id, username, email, first_name, last_name, created_at, total_invested 
            FROM users WHERE referred_by = ? ORDER BY created_at DESC";
    return fetchAll($sql, [$userId]);
}

/**
 * Get referral statistics
 */
function getReferralStats($userId) {
    $stats = [
        'total_referrals' => 0,
        'total_earnings' => 0,
        'level_stats' => []
    ];
    
    // Get total referrals
    $result = fetchRow("SELECT COUNT(*) as count FROM users WHERE referred_by = ?", [$userId]);
    $stats['total_referrals'] = $result ? $result['count'] : 0;
    
    // Get total earnings
    $user = getUserById($userId);
    $stats['total_earnings'] = $user ? $user['referral_earnings'] : 0;
    
    // Get level statistics
    for ($level = 1; $level <= REFERRAL_LEVELS; $level++) {
        $result = fetchRow("SELECT COUNT(*) as count, COALESCE(SUM(amount), 0) as total 
                          FROM referral_commissions WHERE referrer_id = ? AND level = ?", 
                         [$userId, $level]);
        
        $stats['level_stats'][$level] = [
            'count' => $result ? $result['count'] : 0,
            'total' => $result ? $result['total'] : 0
        ];
    }
    
    return $stats;
}

/**
 * Send notification (placeholder for future implementation)
 */
function sendNotification($userId, $type, $message, $data = []) {
    // This can be extended to send email, SMS, or push notifications
    error_log("Notification for user {$userId}: {$message}");
}

/**
 * Log activity
 */
function logActivity($userId, $action, $details = '') {
    $sql = "INSERT INTO activity_logs (user_id, action, details, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?)";
    $params = [
        $userId,
        $action,
        $details,
        $_SERVER['REMOTE_ADDR'] ?? '',
        $_SERVER['HTTP_USER_AGENT'] ?? ''
    ];
    
    executeQuery($sql, $params);
}

/**
 * Get time ago format
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    
    return floor($time/31536000) . ' years ago';
}

/**
 * Redirect function
 */
function redirect($url, $permanent = false) {
    if ($permanent) {
        header('HTTP/1.1 301 Moved Permanently');
    }
    header('Location: ' . $url);
    exit;
}

/**
 * JSON response
 */
function jsonResponse($data, $status = 200) {
    http_response_code($status);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}
?>
