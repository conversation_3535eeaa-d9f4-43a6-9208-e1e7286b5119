<?php
/**
 * AstroGenix - Simple Dashboard
 * Simplified dashboard without complex dependencies
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['logged_in']) || !$_SESSION['logged_in']) {
    header('Location: ../simple_login.php');
    exit;
}

// Simple database connection
try {
    $pdo = new PDO('mysql:host=localhost;dbname=astrogenix;charset=utf8mb4', 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    // Get user data
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
    
    if (!$user) {
        session_destroy();
        header('Location: ../simple_login.php');
        exit;
    }
    
    // Get investment packages
    $packages = $pdo->query("SELECT * FROM investment_packages WHERE is_active = 1 ORDER BY min_amount ASC")->fetchAll();
    
} catch (Exception $e) {
    die("Database error: " . $e->getMessage());
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: ../simple_login.php');
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - AstroGenix</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8fafc;
        }
        
        .navbar {
            background: linear-gradient(135deg, #1e3a8a, #3b82f6) !important;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        
        .package-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        
        .package-card:hover {
            transform: translateY(-5px);
        }
        
        .btn-invest {
            background: linear-gradient(135deg, #10b981, #059669);
            border: none;
            padding: 10px 25px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
        }
        
        .btn-invest:hover {
            background: linear-gradient(135deg, #059669, #047857);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#">
                <i class="fas fa-rocket me-2"></i>AstroGenix
            </a>
            
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    Welcome, <?php echo htmlspecialchars($user['first_name']); ?>!
                </span>
                <a class="nav-link" href="?logout=1">
                    <i class="fas fa-sign-out-alt me-1"></i>Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- User Stats -->
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <div class="ms-3">
                            <h6 class="text-muted mb-0">Balance</h6>
                            <h4 class="mb-0">$<?php echo number_format($user['balance_usdt'], 2); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #3b82f6, #1e40af);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="ms-3">
                            <h6 class="text-muted mb-0">Total Invested</h6>
                            <h4 class="mb-0">$0.00</h4>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="ms-3">
                            <h6 class="text-muted mb-0">Total Earned</h6>
                            <h4 class="mb-0">$0.00</h4>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="ms-3">
                            <h6 class="text-muted mb-0">Referrals</h6>
                            <h4 class="mb-0">0</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Info -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="stats-card">
                    <h5 class="mb-3"><i class="fas fa-user me-2"></i>Account Information</h5>
                    <div class="row">
                        <div class="col-6">
                            <p><strong>Username:</strong><br><?php echo htmlspecialchars($user['username']); ?></p>
                            <p><strong>Email:</strong><br><?php echo htmlspecialchars($user['email']); ?></p>
                        </div>
                        <div class="col-6">
                            <p><strong>Referral Code:</strong><br><?php echo htmlspecialchars($user['referral_code']); ?></p>
                            <p><strong>Member Since:</strong><br><?php echo date('M d, Y', strtotime($user['created_at'])); ?></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="stats-card">
                    <h5 class="mb-3"><i class="fas fa-share-alt me-2"></i>Referral Link</h5>
                    <p class="text-muted">Share your referral link and earn commissions!</p>
                    <div class="input-group">
                        <input type="text" class="form-control" 
                               value="<?php echo $_SERVER['HTTP_HOST']; ?>/register.php?ref=<?php echo $user['referral_code']; ?>" 
                               id="referralLink" readonly>
                        <button class="btn btn-outline-primary" onclick="copyReferralLink()">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Investment Packages -->
        <div class="row mt-4">
            <div class="col-12">
                <h4 class="mb-4"><i class="fas fa-chart-pie me-2"></i>Investment Packages</h4>
            </div>
            
            <?php foreach ($packages as $package): ?>
                <div class="col-md-4">
                    <div class="package-card text-center">
                        <h5 class="text-primary mb-3"><?php echo htmlspecialchars($package['name']); ?></h5>
                        <div class="mb-3">
                            <span class="h3 text-success"><?php echo $package['daily_percentage']; ?>%</span>
                            <small class="text-muted d-block">Daily Profit</small>
                        </div>
                        <div class="mb-3">
                            <strong><?php echo $package['duration']; ?> Days</strong>
                            <small class="text-muted d-block">Duration</small>
                        </div>
                        <div class="mb-4">
                            <div>Min: $<?php echo number_format($package['min_amount'], 2); ?></div>
                            <div>Max: $<?php echo number_format($package['max_amount'], 2); ?></div>
                        </div>
                        <button class="btn btn-invest w-100" onclick="alert('Investment feature coming soon!')">
                            Invest Now
                        </button>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function copyReferralLink() {
            const input = document.getElementById('referralLink');
            input.select();
            document.execCommand('copy');
            alert('Referral link copied to clipboard!');
        }
    </script>
</body>
</html>
