# AstroGenix - Professional USDT Staking Platform

AstroGenix is a modern, secure USDT staking platform built with PHP and MySQL. It features a sleek crypto exchange-style design, comprehensive admin panel, and advanced security measures.

## 🚀 Features

### Core Functionality
- **Investment Packages**: Multiple staking plans with customizable parameters
- **Manual Payout System**: Admin-controlled profit distribution
- **Multi-level Referral Program**: Earn commissions from referrals
- **Task System**: Reward users for completing various tasks
- **News Management**: Admin-controlled news and announcements
- **Support System**: Ticket-based customer support

### Financial Operations
- **Manual Deposits**: Admin-approved deposit system
- **Manual Withdrawals**: Admin-controlled withdrawal processing
- **Registration Bonuses**: Configurable welcome bonuses
- **Transaction History**: Complete financial tracking

### Security Features
- **SQL Injection Protection**: Prepared statements and input validation
- **XSS Protection**: Comprehensive input sanitization
- **CSRF Protection**: Token-based form security
- **Rate Limiting**: Brute force attack prevention
- **Secure Authentication**: Password hashing and session management

### Design & UX
- **Modern Crypto Exchange Style**: Blue, white, and black color scheme
- **Responsive Design**: Mobile-optimized interface
- **Bilingual Support**: English and Russian languages
- **Smooth Animations**: Professional user experience
- **Mobile App Style**: Native app-like mobile interface

## 📋 Requirements

- **PHP**: 7.4 or higher
- **MySQL**: 5.7 or higher
- **Web Server**: Apache or Nginx
- **Extensions**: PDO, PDO_MySQL, JSON, OpenSSL

## 🛠️ Installation

### 1. Download and Extract
```bash
# Clone or download the project
git clone https://github.com/your-repo/astrogenix.git
cd astrogenix
```

### 2. Database Setup
1. Create a new MySQL database:
```sql
CREATE DATABASE astrogenix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. Import the database schema:
```bash
mysql -u your_username -p astrogenix < database/schema.sql
```

3. Import initial data:
```bash
mysql -u your_username -p astrogenix < database/initial_data.sql
```

### 3. Configuration
1. Edit `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'astrogenix');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

2. Update security keys in `config/config.php`:
```php
define('ENCRYPTION_KEY', 'your-unique-encryption-key');
define('JWT_SECRET', 'your-unique-jwt-secret');
define('PASSWORD_SALT', 'your-unique-password-salt');
```

3. Set correct file permissions:
```bash
chmod 755 assets/
chmod 755 uploads/
chmod 644 config/*.php
```

### 4. Web Server Configuration

#### Apache (.htaccess)
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
```

#### Nginx
```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
}

location ~ \.php$ {
    fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
    fastcgi_index index.php;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    include fastcgi_params;
}

# Security headers
add_header X-Content-Type-Options nosniff;
add_header X-Frame-Options DENY;
add_header X-XSS-Protection "1; mode=block";
```

## 🔐 Default Admin Access

After installation, you can access the admin panel with:
- **URL**: `your-domain.com/admin/`
- **Username**: `admin`
- **Password**: `admin123`

**⚠️ Important**: Change the default admin password immediately after first login!

## 📱 Usage

### User Features
1. **Registration**: Users can register with referral codes
2. **Dashboard**: View balance, investments, and statistics
3. **Investment**: Choose from available staking packages
4. **Referrals**: Share referral links and earn commissions
5. **Tasks**: Complete tasks for rewards
6. **Support**: Create support tickets

### Admin Features
1. **User Management**: View and manage all users
2. **Investment Packages**: Create and edit staking plans
3. **Manual Payouts**: Process daily profit distributions
4. **Deposit/Withdrawal Management**: Approve financial transactions
5. **News Management**: Create and publish announcements
6. **Settings**: Configure platform parameters

## 🔧 Configuration Options

### Site Settings (Admin Panel)
- Registration bonus amount
- Minimum/maximum withdrawal limits
- Referral commission rates
- Maintenance mode
- Email verification requirements

### Investment Packages
- Package name and description
- Minimum/maximum investment amounts
- Daily profit percentage
- Investment duration
- Total return percentage

### Referral System
- Number of referral levels (default: 3)
- Commission rates per level
- Referral tracking and statistics

## 🛡️ Security Best Practices

1. **Change Default Credentials**: Update admin password immediately
2. **Use HTTPS**: Enable SSL certificate for production
3. **Regular Updates**: Keep PHP and MySQL updated
4. **Backup Database**: Schedule regular database backups
5. **Monitor Logs**: Check error logs regularly
6. **Firewall**: Configure server firewall rules

## 📊 Database Structure

### Key Tables
- `users`: User accounts and balances
- `investment_packages`: Staking plan definitions
- `user_investments`: Active user investments
- `transactions`: Financial transaction history
- `referral_commissions`: Referral earnings
- `admin_users`: Admin account management

## 🌐 API Endpoints

### Public APIs
- `GET /api/language.php`: Get available languages
- `POST /api/language.php`: Change interface language

### Authenticated APIs
- `POST /api/investment.php`: Create new investment
- `GET /api/user-stats.php`: Get user statistics
- `POST /api/support.php`: Create support ticket

## 🎨 Customization

### Styling
- Edit `assets/css/main.css` for design changes
- Modify color variables in CSS `:root` section
- Update logo and branding elements

### Languages
- Add new languages in `config/languages.php`
- Translate all text strings
- Update language switcher options

### Features
- Add new investment package types
- Implement additional payment methods
- Create custom admin reports

## 🐛 Troubleshooting

### Common Issues
1. **Database Connection Error**: Check database credentials
2. **Permission Denied**: Verify file permissions
3. **Session Issues**: Check PHP session configuration
4. **Email Not Working**: Configure SMTP settings

### Debug Mode
Enable debug mode in `config/config.php`:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## 📞 Support

For technical support and customization requests:
- Email: <EMAIL>
- Documentation: Check inline code comments
- Issues: Report bugs via GitHub issues

## 📄 License

This project is proprietary software. All rights reserved.

## 🔄 Updates

### Version 1.0.0
- Initial release
- Core staking functionality
- Admin panel
- Security features
- Responsive design

---

**⚠️ Disclaimer**: This platform is for educational purposes. Ensure compliance with local financial regulations before deployment.
