<?php
/**
 * AstroGenix - User Login Page
 * Secure user authentication
 */

require_once 'config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    redirect('/pages/dashboard.php');
}

$error = '';
$success = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token. Please try again.';
    } else {
        $username = sanitizeInput($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        $rememberMe = isset($_POST['remember_me']);
        
        if (empty($username) || empty($password)) {
            $error = 'Please fill in all fields.';
        } else {
            $result = loginUser($username, $password, $rememberMe);
            
            if ($result['success']) {
                redirect('/pages/dashboard.php');
            } else {
                $error = $result['message'];
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="<?php echo $_SESSION['language']; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo t('login_title'); ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Stylesheets -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="/assets/css/main.css" rel="stylesheet">
    
    <style>
        /* Modern Auth Container - Crypto Exchange Style */
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--gradient-secondary);
            position: relative;
            overflow: hidden;
        }

        .auth-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(ellipse at center, rgba(79, 70, 229, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .auth-container::after {
            content: '';
            position: absolute;
            top: 20%;
            right: -20%;
            width: 60%;
            height: 60%;
            background: radial-gradient(circle, rgba(79, 70, 229, 0.05) 0%, transparent 70%);
            border-radius: 50%;
            filter: blur(60px);
            pointer-events: none;
        }

        .auth-card {
            width: 100%;
            max-width: 420px;
            background: var(--gradient-card);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-2xl);
            padding: 2.5rem;
            box-shadow: var(--shadow-2xl);
            position: relative;
            z-index: 1;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        .auth-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-glass);
            border-radius: var(--radius-2xl);
            opacity: 0.5;
            pointer-events: none;
        }
        
        .auth-header {
            text-align: center;
            margin-bottom: 2.5rem;
            position: relative;
            z-index: 1;
        }

        .auth-logo {
            font-size: 2.25rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.75rem;
            letter-spacing: -0.02em;
        }

        .auth-title {
            font-size: 1.75rem;
            margin-bottom: 0.75rem;
            color: var(--text-primary);
            font-weight: 600;
            letter-spacing: -0.025em;
        }

        .auth-subtitle {
            color: var(--text-secondary);
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .form-group {
            margin-bottom: 1.75rem;
            position: relative;
            z-index: 1;
        }

        .form-control {
            height: 52px;
            font-size: 1rem;
            border-radius: var(--radius-lg);
            background: rgba(31, 41, 55, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            transition: all var(--transition-normal);
        }

        .form-control:focus {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg), 0 0 0 3px rgba(79, 70, 229, 0.2);
        }

        .form-check {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }

        .form-check input[type="checkbox"] {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-light);
            border-radius: var(--radius-sm);
        }

        .form-check label {
            font-size: 0.9rem;
            color: var(--text-secondary);
            cursor: pointer;
            user-select: none;
        }
        
        .auth-footer {
            text-align: center;
            margin-top: 2.5rem;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
            position: relative;
            z-index: 1;
        }

        .auth-footer p {
            margin-bottom: 0.75rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .auth-footer a {
            color: var(--primary-light);
            font-weight: 500;
            transition: all var(--transition-normal);
        }

        .auth-footer a:hover {
            color: var(--primary-ultra-light);
            text-decoration: underline;
        }

        .back-home {
            position: absolute;
            top: 2rem;
            left: 2rem;
            z-index: 10;
            transition: all var(--transition-normal);
        }

        .back-home:hover {
            transform: translateX(-4px);
        }

        /* Modern Submit Button */
        .btn-primary {
            height: 52px;
            font-size: 1rem;
            font-weight: 600;
            border-radius: var(--radius-lg);
            background: var(--gradient-primary);
            border: none;
            box-shadow: var(--shadow-lg);
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl), var(--shadow-glow);
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:active {
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <!-- Back to home button -->
        <a href="/" class="back-home btn btn-outline">
            ← <?php echo t('nav_home'); ?>
        </a>

        <div class="auth-card fade-in-up">
            <div class="auth-header">
                <div class="auth-logo"><?php echo SITE_NAME; ?></div>
                <h1 class="auth-title"><?php echo t('login_title'); ?></h1>
                <p class="auth-subtitle">Enter your credentials to access your account</p>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="" class="auth-form">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="form-group">
                    <label for="username" class="form-label"><?php echo t('username'); ?> / <?php echo t('email'); ?></label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-control" 
                        placeholder="Enter your username or email"
                        value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                        required
                        autocomplete="username"
                    >
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label"><?php echo t('password'); ?></label>
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        class="form-control" 
                        placeholder="Enter your password"
                        required
                        autocomplete="current-password"
                    >
                </div>
                
                <div class="form-check">
                    <input type="checkbox" id="remember_me" name="remember_me" value="1">
                    <label for="remember_me"><?php echo t('remember_me'); ?></label>
                </div>
                
                <button type="submit" class="btn btn-primary w-full">
                    <?php echo t('login_button'); ?>
                </button>
            </form>
            
            <div class="auth-footer">
                <p>
                    <a href="/forgot-password.php"><?php echo t('forgot_password'); ?></a>
                </p>
                <p>
                    <?php echo t('dont_have_account'); ?> 
                    <a href="/register.php"><?php echo t('nav_register'); ?></a>
                </p>
            </div>
        </div>
    </div>
    
    <!-- Language switcher -->
    <div style="position: fixed; top: 2rem; right: 2rem; z-index: 1000;">
        <select onchange="changeLanguage(this.value)" class="form-control" style="width: auto;">
            <option value="en" <?php echo $_SESSION['language'] === 'en' ? 'selected' : ''; ?>>EN</option>
            <option value="ru" <?php echo $_SESSION['language'] === 'ru' ? 'selected' : ''; ?>>RU</option>
        </select>
    </div>

    <script>
        // Language switcher
        function changeLanguage(lang) {
            fetch('/api/language.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ language: lang })
            }).then(() => {
                location.reload();
            });
        }
        
        // Form validation
        document.querySelector('.auth-form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                e.preventDefault();
                alert('Please fill in all fields.');
                return false;
            }
            
            if (password.length < 6) {
                e.preventDefault();
                alert('Password must be at least 6 characters long.');
                return false;
            }
        });
        
        // Auto-focus first input
        document.getElementById('username').focus();
    </script>
</body>
</html>
