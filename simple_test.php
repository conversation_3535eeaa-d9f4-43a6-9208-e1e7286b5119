<?php
/**
 * AstroGenix - Simple Database Test
 * Basic test without complex dependencies
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>AstroGenix Simple Test</h1>";

// Database configuration (update these values if needed)
$db_host = 'localhost';
$db_name = 'astrogenix';
$db_user = 'root';
$db_pass = '';

echo "<p><strong>Testing with:</strong> Host: $db_host, Database: $db_name, User: $db_user</p>";

try {
    // Test database connection
    $dsn = "mysql:host=$db_host;dbname=$db_name;charset=utf8mb4";
    $pdo = new PDO($dsn, $db_user, $db_pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Test tables
    $tables = [
        'users' => 'Users table',
        'admin_users' => 'Admin users table',
        'investment_packages' => 'Investment packages table',
        'site_settings' => 'Site settings table',
        'news' => 'News table'
    ];
    
    echo "<h2>Table Check:</h2>";
    foreach ($tables as $table => $description) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $result = $stmt->fetch();
            echo "<p style='color: green;'>✅ $description: {$result['count']} records</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ $description: Error - " . $e->getMessage() . "</p>";
        }
    }
    
    // Test admin user
    try {
        $stmt = $pdo->query("SELECT username, email FROM admin_users WHERE role = 'super_admin' LIMIT 1");
        $admin = $stmt->fetch();
        if ($admin) {
            echo "<p style='color: green;'>✅ Admin user found: {$admin['username']} ({$admin['email']})</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ No admin user found</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Admin check failed: " . $e->getMessage() . "</p>";
    }
    
    // Test investment packages
    try {
        $stmt = $pdo->query("SELECT name, min_amount, max_amount, daily_percentage FROM investment_packages WHERE is_active = 1");
        $packages = $stmt->fetchAll();
        echo "<h3>Investment Packages:</h3>";
        foreach ($packages as $package) {
            echo "<p>📦 {$package['name']}: ${$package['min_amount']} - ${$package['max_amount']} ({$package['daily_percentage']}% daily)</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Packages check failed: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
    echo "<h2 style='color: green;'>🎉 Database is working correctly!</h2>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li><a href='index.php'>Go to main page</a></li>";
    echo "<li><a href='login.php'>Admin login</a> (username: admin, password: admin123)</li>";
    echo "<li><a href='register.php'>User registration</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ Database Connection Failed</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    
    echo "<h3>How to fix:</h3>";
    echo "<ol>";
    echo "<li><strong>Check database credentials:</strong> Update the variables at the top of this file</li>";
    echo "<li><strong>Make sure MySQL is running</strong></li>";
    echo "<li><strong>Create database:</strong> <code>CREATE DATABASE astrogenix;</code></li>";
    echo "<li><strong>Import schema:</strong> Import <code>database/astrogenix_complete.sql</code> in phpMyAdmin</li>";
    echo "</ol>";
    
    echo "<h3>Current settings:</h3>";
    echo "<ul>";
    echo "<li>Host: $db_host</li>";
    echo "<li>Database: $db_name</li>";
    echo "<li>User: $db_user</li>";
    echo "<li>Password: " . (empty($db_pass) ? '(empty)' : '(set)') . "</li>";
    echo "</ul>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>AstroGenix Simple Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: #f8fafc;
            line-height: 1.6;
        }
        h1 { 
            color: #1e3a8a; 
            text-align: center;
            margin-bottom: 30px;
        }
        h2 { color: #374151; }
        h3 { color: #4b5563; margin-top: 20px; }
        p { margin: 10px 0; }
        ul, ol { 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        code {
            background: #f3f4f6;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
        }
        a {
            color: #1e3a8a;
            text-decoration: none;
            font-weight: bold;
        }
        a:hover { text-decoration: underline; }
        hr {
            margin: 30px 0;
            border: none;
            border-top: 2px solid #e5e7eb;
        }
    </style>
</head>
<body>
</body>
</html>
