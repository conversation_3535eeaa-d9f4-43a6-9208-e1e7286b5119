<?php
/**
 * AstroGenix - Investment Page
 * Investment packages and portfolio management
 */

require_once '../config/config.php';

// Require authentication
requireAuth();

// Get current user data
$user = getCurrentUser();
if (!$user) {
    redirect('/login.php');
}

// Handle investment creation
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_investment'])) {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token. Please try again.';
    } else {
        $packageId = (int)($_POST['package_id'] ?? 0);
        $amount = (float)($_POST['amount'] ?? 0);
        
        if ($packageId <= 0 || $amount <= 0) {
            $error = 'Invalid investment data.';
        } else {
            $result = createInvestment($user['id'], $packageId, $amount);
            
            if ($result['success']) {
                $success = 'Investment created successfully! Your daily profits will start tomorrow.';
            } else {
                $error = $result['message'];
            }
        }
    }
}

// Get investment packages
$packages = getInvestmentPackages(true);

// Get user investments
$userInvestments = getUserInvestments($user['id']);

// Separate active and completed investments
$activeInvestments = array_filter($userInvestments, function($inv) {
    return $inv['status'] === 'active';
});

$completedInvestments = array_filter($userInvestments, function($inv) {
    return $inv['status'] === 'completed';
});
?>
<!DOCTYPE html>
<html lang="<?php echo $_SESSION['language']; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo t('invest_title'); ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Stylesheets -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="/assets/css/main.css" rel="stylesheet">
    
    <style>
        .invest-container {
            min-height: 100vh;
            background: var(--bg-primary);
            padding: 2rem 0;
        }
        
        .invest-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .invest-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .invest-subtitle {
            font-size: 1.125rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .packages-section {
            margin-bottom: 4rem;
        }
        
        .section-title {
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: var(--text-primary);
        }
        
        .packages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .package-card {
            background: var(--bg-secondary);
            border: 2px solid var(--border-color);
            border-radius: 1rem;
            padding: 2rem;
            position: relative;
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .package-card:hover {
            border-color: var(--primary-light);
            transform: translateY(-5px);
            box-shadow: var(--shadow-xl);
        }
        
        .package-card.featured {
            border-color: var(--accent-color);
            background: linear-gradient(135deg, var(--bg-secondary) 0%, rgba(6, 182, 212, 0.05) 100%);
        }
        
        .package-card.featured:before {
            content: 'POPULAR';
            position: absolute;
            top: 1rem;
            right: -2rem;
            background: var(--accent-color);
            color: white;
            padding: 0.25rem 3rem;
            font-size: 0.75rem;
            font-weight: 600;
            transform: rotate(45deg);
        }
        
        .package-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .package-name {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }
        
        .package-description {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .package-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 1rem;
            background: var(--bg-tertiary);
            border-radius: 0.5rem;
        }
        
        .stat-label {
            display: block;
            font-size: 0.75rem;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.5rem;
        }
        
        .stat-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-light);
        }
        
        .package-details {
            margin-bottom: 2rem;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .detail-row:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .detail-value {
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .invest-form {
            background: var(--bg-tertiary);
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .amount-input {
            margin-bottom: 1rem;
        }
        
        .amount-input input {
            text-align: center;
            font-size: 1.125rem;
            font-weight: 600;
        }
        
        .quick-amounts {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }
        
        .quick-amount {
            flex: 1;
            min-width: 60px;
            padding: 0.5rem;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 0.25rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.875rem;
        }
        
        .quick-amount:hover {
            border-color: var(--primary-light);
            background: var(--primary-light);
            color: white;
        }
        
        .profit-preview {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .profit-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .profit-row:last-child {
            margin-bottom: 0;
            font-weight: 600;
            color: var(--success-color);
        }
        
        .investments-section {
            margin-top: 4rem;
        }
        
        .investments-tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid var(--border-color);
        }
        
        .tab-button {
            padding: 1rem 2rem;
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .tab-button.active {
            color: var(--primary-light);
            border-bottom-color: var(--primary-light);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .investment-item {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .investment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .investment-name {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .investment-status {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .status-active {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border: 1px solid var(--success-color);
        }
        
        .status-completed {
            background: rgba(6, 182, 212, 0.1);
            color: var(--accent-color);
            border: 1px solid var(--accent-color);
        }
        
        .investment-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }
        
        .detail-item {
            text-align: center;
        }
        
        .detail-item .label {
            display: block;
            font-size: 0.75rem;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.25rem;
        }
        
        .detail-item .value {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .progress-bar-container {
            margin-top: 1rem;
        }
        
        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
        
        @media (max-width: 768px) {
            .packages-grid {
                grid-template-columns: 1fr;
            }
            
            .package-stats {
                grid-template-columns: 1fr;
            }
            
            .quick-amounts {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .investment-details {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .investments-tabs {
                flex-direction: column;
                gap: 0;
            }
            
            .tab-button {
                text-align: left;
                padding: 0.75rem 0;
            }
        }
    </style>
</head>
<body>
    <div class="invest-container">
        <div class="container">
            <!-- Back to Dashboard -->
            <div class="mb-3">
                <a href="/pages/dashboard.php" class="btn btn-outline">← Back to Dashboard</a>
            </div>
            
            <!-- Header -->
            <div class="invest-header">
                <h1 class="invest-title"><?php echo t('invest_title'); ?></h1>
                <p class="invest-subtitle">Choose the perfect investment package and start earning passive income with our secure USDT staking platform.</p>
            </div>
            
            <!-- Alerts -->
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <!-- User Balance -->
            <div class="card mb-4">
                <div class="card-body text-center">
                    <h3>Available Balance</h3>
                    <div class="stat-value">$<?php echo formatCurrency($user['balance_usdt']); ?></div>
                    <p class="text-muted">Ready for investment</p>
                </div>
            </div>
            
            <!-- Investment Packages -->
            <div class="packages-section">
                <h2 class="section-title">Investment Packages</h2>
                
                <div class="packages-grid">
                    <?php foreach ($packages as $index => $package): ?>
                        <?php 
                        $profits = calculateInvestmentProfit($package['min_amount'], $package['daily_percentage'], $package['duration_days']);
                        $isFeatured = $index === 1; // Make second package featured
                        ?>
                        <div class="package-card <?php echo $isFeatured ? 'featured' : ''; ?> fade-in">
                            <div class="package-header">
                                <h3 class="package-name"><?php echo htmlspecialchars($package['name']); ?></h3>
                                <p class="package-description"><?php echo htmlspecialchars($package['description']); ?></p>
                            </div>
                            
                            <div class="package-stats">
                                <div class="stat-item">
                                    <span class="stat-label"><?php echo t('daily_profit'); ?></span>
                                    <span class="stat-value"><?php echo formatPercentage($package['daily_percentage']); ?></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><?php echo t('duration'); ?></span>
                                    <span class="stat-value"><?php echo $package['duration_days']; ?> <?php echo t('days'); ?></span>
                                </div>
                            </div>
                            
                            <div class="package-details">
                                <div class="detail-row">
                                    <span class="detail-label"><?php echo t('min_amount'); ?></span>
                                    <span class="detail-value">$<?php echo formatCurrency($package['min_amount'], 0); ?></span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label"><?php echo t('max_amount'); ?></span>
                                    <span class="detail-value">$<?php echo formatCurrency($package['max_amount'], 0); ?></span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label"><?php echo t('total_return'); ?></span>
                                    <span class="detail-value"><?php echo formatPercentage($package['total_return_percentage']); ?></span>
                                </div>
                            </div>
                            
                            <!-- Investment Form -->
                            <form method="POST" class="invest-form ajax-form">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="create_investment" value="1">
                                <input type="hidden" name="package_id" value="<?php echo $package['id']; ?>">
                                
                                <div class="amount-input">
                                    <label class="form-label">Investment Amount (USDT)</label>
                                    <input 
                                        type="number" 
                                        name="amount" 
                                        class="form-control investment-amount" 
                                        min="<?php echo $package['min_amount']; ?>" 
                                        max="<?php echo min($package['max_amount'], $user['balance_usdt']); ?>" 
                                        step="0.01" 
                                        placeholder="Enter amount"
                                        data-package-id="<?php echo $package['id']; ?>"
                                        data-daily-rate="<?php echo $package['daily_percentage']; ?>"
                                        data-duration="<?php echo $package['duration_days']; ?>"
                                        required
                                    >
                                </div>
                                
                                <div class="quick-amounts">
                                    <div class="quick-amount" onclick="setAmount(this, <?php echo $package['min_amount']; ?>)">
                                        $<?php echo formatCurrency($package['min_amount'], 0); ?>
                                    </div>
                                    <div class="quick-amount" onclick="setAmount(this, <?php echo $package['min_amount'] * 2; ?>)">
                                        $<?php echo formatCurrency($package['min_amount'] * 2, 0); ?>
                                    </div>
                                    <div class="quick-amount" onclick="setAmount(this, <?php echo $package['min_amount'] * 5; ?>)">
                                        $<?php echo formatCurrency($package['min_amount'] * 5, 0); ?>
                                    </div>
                                    <div class="quick-amount" onclick="setAmount(this, <?php echo min($package['max_amount'], $user['balance_usdt']); ?>)">
                                        MAX
                                    </div>
                                </div>
                                
                                <div class="profit-preview" id="profit-preview-<?php echo $package['id']; ?>">
                                    <div class="profit-row">
                                        <span>Daily Profit:</span>
                                        <span class="daily-profit">$0.00</span>
                                    </div>
                                    <div class="profit-row">
                                        <span>Total Profit:</span>
                                        <span class="total-profit">$0.00</span>
                                    </div>
                                    <div class="profit-row">
                                        <span>Total Return:</span>
                                        <span class="total-return">$0.00</span>
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-full">
                                    <?php echo t('invest_now'); ?>
                                </button>
                            </form>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- User Investments -->
            <div class="investments-section">
                <h2 class="section-title">My Investments</h2>
                
                <div class="investments-tabs">
                    <button class="tab-button active" onclick="switchTab('active')">
                        Active Investments (<?php echo count($activeInvestments); ?>)
                    </button>
                    <button class="tab-button" onclick="switchTab('completed')">
                        Completed Investments (<?php echo count($completedInvestments); ?>)
                    </button>
                </div>
                
                <!-- Active Investments -->
                <div id="active-tab" class="tab-content active">
                    <?php if (empty($activeInvestments)): ?>
                        <div class="text-center p-5">
                            <h4>No Active Investments</h4>
                            <p class="text-muted">Start your first investment to begin earning daily profits.</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($activeInvestments as $investment): ?>
                            <div class="investment-item">
                                <div class="investment-header">
                                    <h4 class="investment-name"><?php echo htmlspecialchars($investment['package_name']); ?></h4>
                                    <span class="investment-status status-active">Active</span>
                                </div>
                                
                                <div class="investment-details">
                                    <div class="detail-item">
                                        <span class="label">Investment</span>
                                        <span class="value">$<?php echo formatCurrency($investment['amount']); ?></span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="label">Daily Profit</span>
                                        <span class="value">$<?php echo formatCurrency($investment['daily_profit']); ?></span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="label">Total Earned</span>
                                        <span class="value">$<?php echo formatCurrency($investment['total_profit']); ?></span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="label">Next Payout</span>
                                        <span class="value"><?php echo date('M j, Y', strtotime($investment['next_payout'])); ?></span>
                                    </div>
                                </div>
                                
                                <div class="progress-bar-container">
                                    <div class="progress-label">
                                        <span>Progress: <?php echo $investment['days_completed']; ?>/<?php echo $investment['total_days']; ?> days</span>
                                        <span><?php echo round(($investment['days_completed'] / $investment['total_days']) * 100, 1); ?>%</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: <?php echo ($investment['days_completed'] / $investment['total_days']) * 100; ?>%"></div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                
                <!-- Completed Investments -->
                <div id="completed-tab" class="tab-content">
                    <?php if (empty($completedInvestments)): ?>
                        <div class="text-center p-5">
                            <h4>No Completed Investments</h4>
                            <p class="text-muted">Your completed investments will appear here.</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($completedInvestments as $investment): ?>
                            <div class="investment-item">
                                <div class="investment-header">
                                    <h4 class="investment-name"><?php echo htmlspecialchars($investment['package_name']); ?></h4>
                                    <span class="investment-status status-completed">Completed</span>
                                </div>
                                
                                <div class="investment-details">
                                    <div class="detail-item">
                                        <span class="label">Investment</span>
                                        <span class="value">$<?php echo formatCurrency($investment['amount']); ?></span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="label">Total Earned</span>
                                        <span class="value">$<?php echo formatCurrency($investment['total_profit']); ?></span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="label">Completed</span>
                                        <span class="value"><?php echo $investment['completed_at'] ? date('M j, Y', strtotime($investment['completed_at'])) : 'N/A'; ?></span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="label">ROI</span>
                                        <span class="value"><?php echo formatPercentage(($investment['total_profit'] / $investment['amount']) * 100); ?></span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Set investment amount
        function setAmount(element, amount) {
            const form = element.closest('.invest-form');
            const input = form.querySelector('.investment-amount');
            input.value = amount;
            updateProfitPreview(input);
        }
        
        // Update profit preview
        function updateProfitPreview(input) {
            const amount = parseFloat(input.value) || 0;
            const dailyRate = parseFloat(input.dataset.dailyRate) || 0;
            const duration = parseInt(input.dataset.duration) || 0;
            const packageId = input.dataset.packageId;
            
            const dailyProfit = (amount * dailyRate) / 100;
            const totalProfit = dailyProfit * duration;
            const totalReturn = amount + totalProfit;
            
            const preview = document.getElementById(`profit-preview-${packageId}`);
            if (preview) {
                preview.querySelector('.daily-profit').textContent = '$' + dailyProfit.toFixed(2);
                preview.querySelector('.total-profit').textContent = '$' + totalProfit.toFixed(2);
                preview.querySelector('.total-return').textContent = '$' + totalReturn.toFixed(2);
            }
        }
        
        // Switch tabs
        function switchTab(tab) {
            // Update tab buttons
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Update tab content
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.getElementById(tab + '-tab').classList.add('active');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners to amount inputs
            document.querySelectorAll('.investment-amount').forEach(input => {
                input.addEventListener('input', function() {
                    updateProfitPreview(this);
                });
                
                // Initial calculation
                updateProfitPreview(input);
            });
        });
    </script>
</body>
</html>
