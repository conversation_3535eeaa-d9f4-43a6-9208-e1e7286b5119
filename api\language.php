<?php
/**
 * AstroGenix - Language API
 * Handle language switching
 */

require_once '../config/config.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Get JSON input
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['language'])) {
            throw new Exception('Invalid input data');
        }
        
        $language = sanitizeInput($input['language'], 'alphanumeric');
        
        // Validate language
        if (!in_array($language, ['en', 'ru'])) {
            throw new Exception('Invalid language code');
        }
        
        // Update session language
        $_SESSION['language'] = $language;
        
        // Log language change
        if (isLoggedIn()) {
            logActivity(getCurrentUserId(), 'language_changed', "Language changed to: {$language}");
        }
        
        jsonResponse([
            'success' => true,
            'message' => 'Language updated successfully',
            'language' => $language
        ]);
        
    } else {
        // GET request - return current language
        jsonResponse([
            'success' => true,
            'language' => $_SESSION['language'] ?? 'en',
            'available_languages' => getAvailableLanguages()
        ]);
    }
    
} catch (Exception $e) {
    error_log("Language API error: " . $e->getMessage());
    jsonResponse([
        'success' => false,
        'message' => 'Failed to update language'
    ], 400);
}
?>
