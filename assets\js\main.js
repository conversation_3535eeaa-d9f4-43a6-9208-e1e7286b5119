/**
 * AstroGenix - Main JavaScript Functions
 * Core functionality and utilities
 */

// Global configuration
const AstroGenix = {
    apiUrl: '/api',
    language: document.documentElement.lang || 'en',
    csrfToken: document.querySelector('meta[name="csrf-token"]')?.content || '',
    
    // Initialize the application
    init() {
        this.setupEventListeners();
        this.initAnimations();
        this.initTooltips();
        this.startAutoRefresh();
    },
    
    // Setup global event listeners
    setupEventListeners() {
        // Form submissions
        document.addEventListener('submit', this.handleFormSubmit.bind(this));
        
        // AJAX links
        document.addEventListener('click', this.handleAjaxLinks.bind(this));
        
        // Copy to clipboard
        document.addEventListener('click', this.handleCopyToClipboard.bind(this));
        
        // Modal triggers
        document.addEventListener('click', this.handleModalTriggers.bind(this));
        
        // Responsive navigation
        this.setupResponsiveNav();
    },
    
    // Handle form submissions with AJAX
    handleFormSubmit(e) {
        const form = e.target;
        if (!form.classList.contains('ajax-form')) return;
        
        e.preventDefault();
        this.submitForm(form);
    },
    
    // Submit form via AJAX
    async submitForm(form) {
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn?.textContent;
        
        try {
            // Show loading state
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="spinner"></span> Loading...';
            }
            
            const response = await fetch(form.action || window.location.href, {
                method: form.method || 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification(result.message || 'Success!', 'success');
                
                // Handle redirect
                if (result.redirect) {
                    setTimeout(() => {
                        window.location.href = result.redirect;
                    }, 1000);
                }
                
                // Reset form if specified
                if (result.reset_form) {
                    form.reset();
                }
                
                // Trigger custom event
                form.dispatchEvent(new CustomEvent('ajaxSuccess', { detail: result }));
                
            } else {
                this.showNotification(result.message || 'An error occurred', 'error');
                
                // Show field errors
                if (result.errors) {
                    this.showFieldErrors(form, result.errors);
                }
            }
            
        } catch (error) {
            console.error('Form submission error:', error);
            this.showNotification('Network error occurred', 'error');
        } finally {
            // Restore button state
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            }
        }
    },
    
    // Handle AJAX links
    handleAjaxLinks(e) {
        const link = e.target.closest('.ajax-link');
        if (!link) return;
        
        e.preventDefault();
        this.loadContent(link.href, link.dataset.target);
    },
    
    // Load content via AJAX
    async loadContent(url, target = '#main-content') {
        try {
            this.showLoading(target);
            
            const response = await fetch(url, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const html = await response.text();
            const targetElement = document.querySelector(target);
            
            if (targetElement) {
                targetElement.innerHTML = html;
                this.initAnimations(targetElement);
            }
            
        } catch (error) {
            console.error('Content loading error:', error);
            this.showNotification('Failed to load content', 'error');
        }
    },
    
    // Handle copy to clipboard
    handleCopyToClipboard(e) {
        const copyBtn = e.target.closest('.copy-btn');
        if (!copyBtn) return;
        
        const text = copyBtn.dataset.copy || copyBtn.textContent;
        
        navigator.clipboard.writeText(text).then(() => {
            this.showNotification('Copied to clipboard!', 'success');
            
            // Visual feedback
            const originalText = copyBtn.textContent;
            copyBtn.textContent = 'Copied!';
            setTimeout(() => {
                copyBtn.textContent = originalText;
            }, 2000);
        }).catch(() => {
            this.showNotification('Failed to copy', 'error');
        });
    },
    
    // Handle modal triggers
    handleModalTriggers(e) {
        const trigger = e.target.closest('[data-modal]');
        if (!trigger) return;
        
        e.preventDefault();
        const modalId = trigger.dataset.modal;
        this.openModal(modalId);
    },
    
    // Show notification
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;
        
        // Add to page
        let container = document.querySelector('.notifications-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'notifications-container';
            document.body.appendChild(container);
        }
        
        container.appendChild(notification);
        
        // Auto remove
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, duration);
        
        // Animate in
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
    },
    
    // Show field errors
    showFieldErrors(form, errors) {
        // Clear existing errors
        form.querySelectorAll('.field-error').forEach(el => el.remove());
        form.querySelectorAll('.error').forEach(el => el.classList.remove('error'));
        
        // Show new errors
        Object.keys(errors).forEach(field => {
            const input = form.querySelector(`[name="${field}"]`);
            if (input) {
                input.classList.add('error');
                
                const errorDiv = document.createElement('div');
                errorDiv.className = 'field-error';
                errorDiv.textContent = Array.isArray(errors[field]) ? errors[field][0] : errors[field];
                
                input.parentElement.appendChild(errorDiv);
            }
        });
    },
    
    // Show loading state
    showLoading(target) {
        const element = document.querySelector(target);
        if (element) {
            element.innerHTML = '<div class="loading-spinner"><div class="spinner"></div><p>Loading...</p></div>';
        }
    },
    
    // Initialize animations
    initAnimations(container = document) {
        // Fade in elements
        const fadeElements = container.querySelectorAll('.fade-in');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });
        
        fadeElements.forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });
        
        // Counter animations
        this.initCounters(container);
    },
    
    // Initialize counter animations
    initCounters(container = document) {
        const counters = container.querySelectorAll('.counter');
        
        counters.forEach(counter => {
            const target = parseFloat(counter.dataset.target || counter.textContent);
            const duration = parseInt(counter.dataset.duration || 2000);
            const start = parseFloat(counter.dataset.start || 0);
            
            this.animateCounter(counter, start, target, duration);
        });
    },
    
    // Animate counter
    animateCounter(element, start, end, duration) {
        const startTime = performance.now();
        const difference = end - start;
        
        const step = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const current = start + (difference * this.easeOutQuart(progress));
            element.textContent = this.formatNumber(current);
            
            if (progress < 1) {
                requestAnimationFrame(step);
            }
        };
        
        requestAnimationFrame(step);
    },
    
    // Easing function
    easeOutQuart(t) {
        return 1 - Math.pow(1 - t, 4);
    },
    
    // Format number for display
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        } else {
            return num.toFixed(0);
        }
    },
    
    // Initialize tooltips
    initTooltips() {
        const tooltips = document.querySelectorAll('[data-tooltip]');
        
        tooltips.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target, e.target.dataset.tooltip);
            });
            
            element.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    },
    
    // Show tooltip
    showTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = text;
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
        
        setTimeout(() => tooltip.classList.add('show'), 10);
    },
    
    // Hide tooltip
    hideTooltip() {
        const tooltip = document.querySelector('.tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    },
    
    // Setup responsive navigation
    setupResponsiveNav() {
        const navToggle = document.querySelector('.nav-toggle');
        const navMenu = document.querySelector('.nav-menu');
        
        if (navToggle && navMenu) {
            navToggle.addEventListener('click', () => {
                navMenu.classList.toggle('active');
                navToggle.classList.toggle('active');
            });
            
            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!navToggle.contains(e.target) && !navMenu.contains(e.target)) {
                    navMenu.classList.remove('active');
                    navToggle.classList.remove('active');
                }
            });
        }
    },
    
    // Open modal
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
            
            // Close on backdrop click
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeModal(modalId);
                }
            });
        }
    },
    
    // Close modal
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('active');
            document.body.style.overflow = '';
        }
    },
    
    // Start auto-refresh for dynamic content
    startAutoRefresh() {
        // Refresh balance and stats every 30 seconds
        setInterval(() => {
            this.refreshUserStats();
        }, 30000);
    },
    
    // Refresh user statistics
    async refreshUserStats() {
        try {
            const response = await fetch('/api/user-stats.php');
            const data = await response.json();
            
            if (data.success) {
                // Update balance displays
                document.querySelectorAll('.user-balance').forEach(el => {
                    el.textContent = '$' + this.formatCurrency(data.balance);
                });
                
                // Update other stats
                if (data.stats) {
                    Object.keys(data.stats).forEach(key => {
                        const elements = document.querySelectorAll(`[data-stat="${key}"]`);
                        elements.forEach(el => {
                            el.textContent = data.stats[key];
                        });
                    });
                }
            }
        } catch (error) {
            console.error('Failed to refresh user stats:', error);
        }
    },
    
    // Format currency
    formatCurrency(amount, decimals = 2) {
        return parseFloat(amount).toLocaleString('en-US', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    },
    
    // Validate form
    validateForm(form) {
        const errors = {};
        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
        
        inputs.forEach(input => {
            if (!input.value.trim()) {
                errors[input.name] = 'This field is required';
            }
        });
        
        // Email validation
        const emailInputs = form.querySelectorAll('input[type="email"]');
        emailInputs.forEach(input => {
            if (input.value && !this.isValidEmail(input.value)) {
                errors[input.name] = 'Please enter a valid email address';
            }
        });
        
        return Object.keys(errors).length === 0 ? null : errors;
    },
    
    // Email validation
    isValidEmail(email) {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    AstroGenix.init();
});

// Export for global use
window.AstroGenix = AstroGenix;
