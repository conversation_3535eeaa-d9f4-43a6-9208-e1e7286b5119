<?php
/**
 * AstroGenix - Minimal Test
 * Test without any includes to isolate issues
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>AstroGenix Minimal Test</h1>";

// Test 1: Basic PHP
echo "<p>✅ PHP is working: " . PHP_VERSION . "</p>";

// Test 2: Session
session_start();
echo "<p>✅ Session started</p>";

// Test 3: Database connection
try {
    $pdo = new PDO('mysql:host=localhost;dbname=astrogenix;charset=utf8mb4', 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    echo "<p>✅ Database connection successful</p>";
    
    // Test basic query
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "<p>✅ Database query works: {$result['count']} users</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

// Test 4: File existence
$files = [
    'config/config.php',
    'config/database.php', 
    'config/languages.php',
    'includes/functions.php',
    'includes/security.php',
    'includes/auth.php',
    'includes/language.php'
];

echo "<h2>File Check:</h2>";
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<p>✅ $file exists</p>";
    } else {
        echo "<p style='color: red;'>❌ $file missing</p>";
    }
}

// Test 5: Try to include files one by one
echo "<h2>Include Test:</h2>";

try {
    define('ROOT_PATH', __DIR__);
    echo "<p>✅ ROOT_PATH defined</p>";
    
    require_once 'config/database.php';
    echo "<p>✅ database.php included</p>";
    
    require_once 'includes/security.php';
    echo "<p>✅ security.php included</p>";
    
    require_once 'includes/language.php';
    echo "<p>✅ language.php included</p>";
    
    require_once 'config/languages.php';
    echo "<p>✅ languages.php included</p>";
    
    require_once 'includes/functions.php';
    echo "<p>✅ functions.php included</p>";
    
    require_once 'includes/auth.php';
    echo "<p>✅ auth.php included</p>";
    
    echo "<h2 style='color: green;'>🎉 All files included successfully!</h2>";
    echo "<p><a href='index.php'>Try main page now</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ Include Error:</h2>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>AstroGenix Minimal Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: #f8fafc;
        }
        h1 { 
            color: #1e3a8a; 
            text-align: center;
        }
        h2 { color: #374151; }
        p { margin: 10px 0; }
        a {
            color: #1e3a8a;
            text-decoration: none;
            font-weight: bold;
        }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
</body>
</html>
