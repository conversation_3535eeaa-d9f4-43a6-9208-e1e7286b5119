<?php
/**
 * AstroGenix Main Configuration
 * Core application settings and constants
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    // Set session parameters before starting
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 0); // Set to 1 for HTTPS
    ini_set('session.use_strict_mode', 1);

    session_start();
}

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('UTC');

// Site configuration
define('SITE_NAME', 'AstroGenix');
define('SITE_URL', 'http://localhost/astrogen');
define('SITE_EMAIL', '<EMAIL>');
define('SITE_VERSION', '1.0.0');

// Security settings
define('ENCRYPTION_KEY', 'your-secret-encryption-key-change-this');
define('JWT_SECRET', 'your-jwt-secret-key-change-this');
define('PASSWORD_SALT', 'your-password-salt-change-this');

// Session settings (already set above before session_start)

// File paths
define('ROOT_PATH', dirname(__DIR__));
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('ASSETS_PATH', ROOT_PATH . '/assets');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');

// Include required files in correct order
try {
    require_once ROOT_PATH . '/config/database.php';
    require_once INCLUDES_PATH . '/security.php';
    require_once INCLUDES_PATH . '/language.php';
    require_once INCLUDES_PATH . '/functions.php';
    require_once INCLUDES_PATH . '/auth.php';
} catch (Exception $e) {
    error_log("Failed to include required files: " . $e->getMessage());
}

// Default language
if (!isset($_SESSION['language'])) {
    $_SESSION['language'] = 'en';
}

// Load site settings from database
function loadSiteSettings() {
    static $settings = null;

    if ($settings === null) {
        $settings = [];
        try {
            // Check if database connection exists
            if (function_exists('fetchAll')) {
                $result = fetchAll("SELECT setting_key, setting_value, setting_type FROM site_settings");
                if ($result) {
                    foreach ($result as $row) {
                        $value = $row['setting_value'];

                        // Convert value based on type
                        switch ($row['setting_type']) {
                            case 'number':
                                $value = (float)$value;
                                break;
                            case 'boolean':
                                $value = ($value === 'true' || $value === '1');
                                break;
                            case 'json':
                                $value = json_decode($value, true);
                                break;
                        }

                        $settings[$row['setting_key']] = $value;
                    }
                }
            }
        } catch (Exception $e) {
            error_log("Failed to load site settings: " . $e->getMessage());
        }
    }

    return $settings;
}

// Get site setting
function getSetting($key, $default = null) {
    $settings = loadSiteSettings();
    return isset($settings[$key]) ? $settings[$key] : $default;
}

// Update site setting
function updateSetting($key, $value, $type = 'text') {
    try {
        $sql = "INSERT INTO site_settings (setting_key, setting_value, setting_type) 
                VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE setting_value = ?, setting_type = ?";
        return executeQuery($sql, [$key, $value, $type, $value, $type]);
    } catch (Exception $e) {
        error_log("Failed to update setting: " . $e->getMessage());
        return false;
    }
}

// Application constants
define('MIN_PASSWORD_LENGTH', 8);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// Investment constants
define('MIN_INVESTMENT', 10);
define('MAX_INVESTMENT', 100000);

// These will be loaded dynamically from database
// define('MIN_WITHDRAWAL', getSetting('min_withdrawal', 5));
// define('MAX_WITHDRAWAL', getSetting('max_withdrawal', 10000));

// Referral constants (default values, will be overridden by database settings)
define('REFERRAL_LEVELS', 3);
define('REFERRAL_LEVEL_1', 10);
define('REFERRAL_LEVEL_2', 5);
define('REFERRAL_LEVEL_3', 2);

// Email configuration (for future use)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_ENCRYPTION', 'tls');

// API configuration
define('API_VERSION', 'v1');
define('API_RATE_LIMIT', 100); // requests per hour

// File upload settings
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']);

// Pagination
define('ITEMS_PER_PAGE', 20);

// Cache settings
define('CACHE_ENABLED', false);
define('CACHE_DURATION', 3600); // 1 hour

// Maintenance mode check (will be checked later after functions are loaded)
// if (getSetting('maintenance_mode', false) && !isAdmin()) {
//     include ROOT_PATH . '/maintenance.php';
//     exit;
// }

// Auto-load language file
if (function_exists('loadLanguage')) {
    loadLanguage($_SESSION['language']);
}

// Helper function to check if user is admin
function isAdmin() {
    return isset($_SESSION['admin_id']) && $_SESSION['admin_id'] > 0;
}

// Helper function to check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']) && $_SESSION['user_id'] > 0;
}

// Helper function to get current user ID
function getCurrentUserId() {
    return isset($_SESSION['user_id']) ? (int)$_SESSION['user_id'] : 0;
}

// Helper function to get current admin ID
function getCurrentAdminId() {
    return isset($_SESSION['admin_id']) ? (int)$_SESSION['admin_id'] : 0;
}

// CSRF token functions
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Initialize CSRF token
generateCSRFToken();
?>
