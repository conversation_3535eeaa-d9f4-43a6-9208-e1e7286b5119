<?php
/**
 * AstroGenix - User Registration Page
 * New user account creation
 */

require_once 'config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    redirect('/pages/dashboard.php');
}

$error = '';
$success = '';
$errors = [];

// Handle registration form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token. Please try again.';
    } else {
        $data = [
            'username' => sanitizeInput($_POST['username'] ?? '', 'username'),
            'email' => sanitizeInput($_POST['email'] ?? '', 'email'),
            'password' => $_POST['password'] ?? '',
            'confirm_password' => $_POST['confirm_password'] ?? '',
            'first_name' => sanitizeInput($_POST['first_name'] ?? ''),
            'last_name' => sanitizeInput($_POST['last_name'] ?? ''),
            'phone' => sanitizeInput($_POST['phone'] ?? '', 'phone'),
            'country' => sanitizeInput($_POST['country'] ?? ''),
            'referral_code' => sanitizeInput($_POST['referral_code'] ?? '', 'alphanumeric')
        ];
        
        // Check password confirmation
        if ($data['password'] !== $data['confirm_password']) {
            $errors['confirm_password'] = ['Passwords do not match'];
        }
        
        // Check terms acceptance
        if (!isset($_POST['accept_terms'])) {
            $errors['accept_terms'] = ['You must accept the terms and conditions'];
        }
        
        if (empty($errors)) {
            $result = registerUser($data);
            
            if ($result['success']) {
                $success = 'Registration successful! You can now login to your account.';
                // Clear form data
                $data = [];
            } else {
                if (isset($result['errors'])) {
                    $errors = $result['errors'];
                } else {
                    $error = $result['message'];
                }
            }
        }
    }
}

// Get referral code from URL if present
$referralCode = sanitizeInput($_GET['ref'] ?? '', 'alphanumeric');
?>
<!DOCTYPE html>
<html lang="<?php echo $_SESSION['language']; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo t('register_title'); ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Stylesheets -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="/assets/css/main.css" rel="stylesheet">
    
    <style>
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--gradient-secondary);
            position: relative;
            overflow: hidden;
            padding: 2rem 0;
        }
        
        .auth-container:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23334155" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.1;
        }
        
        .auth-card {
            width: 100%;
            max-width: 500px;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: var(--shadow-xl);
            position: relative;
            z-index: 1;
        }
        
        .auth-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .auth-logo {
            font-size: 2rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }
        
        .auth-title {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }
        
        .auth-subtitle {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .form-row {
            display: flex;
            gap: 1rem;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .form-control {
            height: 48px;
            font-size: 1rem;
        }
        
        .form-control.error {
            border-color: var(--error-color);
        }
        
        .field-error {
            color: var(--error-color);
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }
        
        .form-check {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }
        
        .form-check input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: var(--primary-light);
            margin-top: 0.125rem;
        }
        
        .form-check label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            cursor: pointer;
            line-height: 1.4;
        }
        
        .auth-footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
        }
        
        .auth-footer p {
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .auth-footer a {
            color: var(--primary-light);
            font-weight: 500;
        }
        
        .back-home {
            position: absolute;
            top: 2rem;
            left: 2rem;
            z-index: 2;
        }
        
        .registration-bonus {
            background: var(--gradient-accent);
            color: white;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: 500;
        }
        
        .password-strength {
            margin-top: 0.5rem;
        }
        
        .strength-bar {
            height: 4px;
            background: var(--border-color);
            border-radius: 2px;
            overflow: hidden;
        }
        
        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }
        
        .strength-weak { background: var(--error-color); width: 25%; }
        .strength-fair { background: var(--warning-color); width: 50%; }
        .strength-good { background: var(--accent-color); width: 75%; }
        .strength-strong { background: var(--success-color); width: 100%; }
        
        .strength-text {
            font-size: 0.75rem;
            margin-top: 0.25rem;
            color: var(--text-muted);
        }
        
        @media (max-width: 768px) {
            .auth-card {
                margin: 1rem;
                padding: 1.5rem;
            }
            
            .form-row {
                flex-direction: column;
                gap: 0;
            }
            
            .back-home {
                position: static;
                margin-bottom: 1rem;
                display: inline-block;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <!-- Back to home button -->
        <a href="/" class="back-home btn btn-outline">
            ← <?php echo t('nav_home'); ?>
        </a>
        
        <div class="auth-card fade-in">
            <div class="auth-header">
                <div class="auth-logo"><?php echo SITE_NAME; ?></div>
                <h1 class="auth-title"><?php echo t('register_title'); ?></h1>
                <p class="auth-subtitle">Create your account and start earning today</p>
            </div>
            
            <!-- Registration bonus notice -->
            <div class="registration-bonus">
                🎉 Get $<?php echo getSetting('registration_bonus', 10); ?> bonus when you register!
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success); ?>
                    <br><a href="/login.php" class="btn btn-primary mt-2">Login Now</a>
                </div>
            <?php else: ?>
            
            <form method="POST" action="" class="auth-form" id="registerForm">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="first_name" class="form-label"><?php echo t('first_name'); ?> *</label>
                        <input 
                            type="text" 
                            id="first_name" 
                            name="first_name" 
                            class="form-control <?php echo isset($errors['first_name']) ? 'error' : ''; ?>" 
                            placeholder="Enter your first name"
                            value="<?php echo htmlspecialchars($data['first_name'] ?? ''); ?>"
                            required
                        >
                        <?php if (isset($errors['first_name'])): ?>
                            <div class="field-error"><?php echo implode(', ', $errors['first_name']); ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="form-group">
                        <label for="last_name" class="form-label"><?php echo t('last_name'); ?> *</label>
                        <input 
                            type="text" 
                            id="last_name" 
                            name="last_name" 
                            class="form-control <?php echo isset($errors['last_name']) ? 'error' : ''; ?>" 
                            placeholder="Enter your last name"
                            value="<?php echo htmlspecialchars($data['last_name'] ?? ''); ?>"
                            required
                        >
                        <?php if (isset($errors['last_name'])): ?>
                            <div class="field-error"><?php echo implode(', ', $errors['last_name']); ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="username" class="form-label"><?php echo t('username'); ?> *</label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-control <?php echo isset($errors['username']) ? 'error' : ''; ?>" 
                        placeholder="Choose a unique username"
                        value="<?php echo htmlspecialchars($data['username'] ?? ''); ?>"
                        required
                        autocomplete="username"
                    >
                    <?php if (isset($errors['username'])): ?>
                        <div class="field-error"><?php echo implode(', ', $errors['username']); ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="email" class="form-label"><?php echo t('email'); ?> *</label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        class="form-control <?php echo isset($errors['email']) ? 'error' : ''; ?>" 
                        placeholder="Enter your email address"
                        value="<?php echo htmlspecialchars($data['email'] ?? ''); ?>"
                        required
                        autocomplete="email"
                    >
                    <?php if (isset($errors['email'])): ?>
                        <div class="field-error"><?php echo implode(', ', $errors['email']); ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="phone" class="form-label"><?php echo t('phone'); ?></label>
                        <input 
                            type="tel" 
                            id="phone" 
                            name="phone" 
                            class="form-control" 
                            placeholder="Your phone number"
                            value="<?php echo htmlspecialchars($data['phone'] ?? ''); ?>"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="country" class="form-label"><?php echo t('country'); ?></label>
                        <input 
                            type="text" 
                            id="country" 
                            name="country" 
                            class="form-control" 
                            placeholder="Your country"
                            value="<?php echo htmlspecialchars($data['country'] ?? ''); ?>"
                        >
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label"><?php echo t('password'); ?> *</label>
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        class="form-control <?php echo isset($errors['password']) ? 'error' : ''; ?>" 
                        placeholder="Create a strong password"
                        required
                        autocomplete="new-password"
                    >
                    <div class="password-strength">
                        <div class="strength-bar">
                            <div class="strength-fill" id="strengthFill"></div>
                        </div>
                        <div class="strength-text" id="strengthText">Password strength</div>
                    </div>
                    <?php if (isset($errors['password'])): ?>
                        <div class="field-error"><?php echo implode(', ', $errors['password']); ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="confirm_password" class="form-label"><?php echo t('confirm_password'); ?> *</label>
                    <input 
                        type="password" 
                        id="confirm_password" 
                        name="confirm_password" 
                        class="form-control <?php echo isset($errors['confirm_password']) ? 'error' : ''; ?>" 
                        placeholder="Confirm your password"
                        required
                        autocomplete="new-password"
                    >
                    <?php if (isset($errors['confirm_password'])): ?>
                        <div class="field-error"><?php echo implode(', ', $errors['confirm_password']); ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="referral_code" class="form-label"><?php echo t('referral_code'); ?> (Optional)</label>
                    <input 
                        type="text" 
                        id="referral_code" 
                        name="referral_code" 
                        class="form-control" 
                        placeholder="Enter referral code if you have one"
                        value="<?php echo htmlspecialchars($referralCode ?: ($data['referral_code'] ?? '')); ?>"
                    >
                </div>
                
                <div class="form-check">
                    <input type="checkbox" id="accept_terms" name="accept_terms" value="1" required>
                    <label for="accept_terms">
                        I agree to the <a href="/terms.php" target="_blank">Terms of Service</a> 
                        and <a href="/privacy.php" target="_blank">Privacy Policy</a>
                    </label>
                    <?php if (isset($errors['accept_terms'])): ?>
                        <div class="field-error"><?php echo implode(', ', $errors['accept_terms']); ?></div>
                    <?php endif; ?>
                </div>
                
                <button type="submit" class="btn btn-primary w-full">
                    <?php echo t('register_button'); ?>
                </button>
            </form>
            
            <?php endif; ?>
            
            <div class="auth-footer">
                <p>
                    <?php echo t('already_have_account'); ?> 
                    <a href="/login.php"><?php echo t('nav_login'); ?></a>
                </p>
            </div>
        </div>
    </div>
    
    <!-- Language switcher -->
    <div style="position: fixed; top: 2rem; right: 2rem; z-index: 1000;">
        <select onchange="changeLanguage(this.value)" class="form-control" style="width: auto;">
            <option value="en" <?php echo $_SESSION['language'] === 'en' ? 'selected' : ''; ?>>EN</option>
            <option value="ru" <?php echo $_SESSION['language'] === 'ru' ? 'selected' : ''; ?>>RU</option>
        </select>
    </div>

    <script>
        // Language switcher
        function changeLanguage(lang) {
            fetch('/api/language.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ language: lang })
            }).then(() => {
                location.reload();
            });
        }
        
        // Password strength checker
        const passwordInput = document.getElementById('password');
        const strengthFill = document.getElementById('strengthFill');
        const strengthText = document.getElementById('strengthText');
        
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            const strength = calculatePasswordStrength(password);
            
            strengthFill.className = 'strength-fill strength-' + strength.level;
            strengthText.textContent = strength.text;
        });
        
        function calculatePasswordStrength(password) {
            let score = 0;
            
            if (password.length >= 8) score++;
            if (password.match(/[a-z]/)) score++;
            if (password.match(/[A-Z]/)) score++;
            if (password.match(/[0-9]/)) score++;
            if (password.match(/[^a-zA-Z0-9]/)) score++;
            
            const levels = ['weak', 'weak', 'fair', 'good', 'strong'];
            const texts = ['Weak', 'Weak', 'Fair', 'Good', 'Strong'];
            
            return {
                level: levels[score] || 'weak',
                text: texts[score] || 'Weak'
            };
        }
        
        // Form validation
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Passwords do not match.');
                return false;
            }
            
            if (password.length < 8) {
                e.preventDefault();
                alert('Password must be at least 8 characters long.');
                return false;
            }
            
            if (!document.getElementById('accept_terms').checked) {
                e.preventDefault();
                alert('You must accept the terms and conditions.');
                return false;
            }
        });
        
        // Auto-focus first input
        document.getElementById('first_name').focus();
    </script>
</body>
</html>
