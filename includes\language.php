<?php
/**
 * AstroGenix Language Functions
 * Language support functions
 */

// Prevent direct access
if (!defined('ROOT_PATH')) {
    die('Direct access not allowed');
}

// Include language definitions
require_once ROOT_PATH . '/config/languages.php';

// Load language function
if (!function_exists('loadLanguage')) {
    function loadLanguage($lang) {
        if (in_array($lang, ['en', 'ru'])) {
            $_SESSION['language'] = $lang;
            global $current_language;
            $current_language = $lang;
        }
    }
}

// Initialize language if not set
if (!isset($_SESSION['language'])) {
    $_SESSION['language'] = 'en';
}

// Load current language
$current_language = $_SESSION['language'];
?>
