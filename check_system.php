<?php
/**
 * AstroGenix - System Check
 * Comprehensive system and configuration check
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>AstroGenix System Check</h1>";

$errors = [];
$warnings = [];
$success = [];

// Check PHP version
if (version_compare(PHP_VERSION, '7.4.0', '>=')) {
    $success[] = "PHP version: " . PHP_VERSION . " ✅";
} else {
    $errors[] = "PHP version " . PHP_VERSION . " is too old. Requires 7.4+";
}

// Check required PHP extensions
$required_extensions = ['pdo', 'pdo_mysql', 'json', 'mbstring', 'openssl'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        $success[] = "PHP extension '$ext' loaded ✅";
    } else {
        $errors[] = "Required PHP extension '$ext' not loaded ❌";
    }
}

// Check file permissions
$directories = ['assets', 'uploads', 'config'];
foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            $success[] = "Directory '$dir' is writable ✅";
        } else {
            $warnings[] = "Directory '$dir' is not writable ⚠️";
        }
    } else {
        $warnings[] = "Directory '$dir' does not exist ⚠️";
    }
}

// Check configuration files
$config_files = [
    'config/database.php',
    'config/config.php',
    'config/languages.php'
];

foreach ($config_files as $file) {
    if (file_exists($file)) {
        $success[] = "Configuration file '$file' exists ✅";
    } else {
        $errors[] = "Configuration file '$file' missing ❌";
    }
}

// Check includes files
$include_files = [
    'includes/functions.php',
    'includes/security.php',
    'includes/auth.php',
    'includes/language.php'
];

foreach ($include_files as $file) {
    if (file_exists($file)) {
        $success[] = "Include file '$file' exists ✅";
    } else {
        $errors[] = "Include file '$file' missing ❌";
    }
}

// Try to test database connection directly
try {
    // Database configuration
    $db_config = [
        'host' => 'localhost',
        'name' => 'astrogenix',
        'user' => 'root',
        'pass' => '',
        'charset' => 'utf8mb4'
    ];

    // Try to connect to database
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['name']};charset={$db_config['charset']}";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];

    $pdo = new PDO($dsn, $db_config['user'], $db_config['pass'], $options);
    $success[] = "Database connection successful ✅";

    // Test basic queries
    $tables = ['users', 'admin_users', 'investment_packages', 'site_settings'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            $success[] = "Table '$table' accessible ($count records) ✅";
        } catch (Exception $e) {
            $errors[] = "Table '$table' not accessible: " . $e->getMessage() . " ❌";
        }
    }

    // Now try to load configuration
    try {
        require_once 'config/config.php';
        $success[] = "Configuration loaded successfully ✅";
    } catch (Exception $e) {
        $warnings[] = "Configuration loading failed: " . $e->getMessage() . " ⚠️";
    }

} catch (Exception $e) {
    $errors[] = "Database connection failed: " . $e->getMessage() . " ❌";
    $warnings[] = "Please check database credentials in config/database.php";
}

// Display results
echo "<h2>System Check Results</h2>";

if (!empty($success)) {
    echo "<h3 style='color: green;'>✅ Success</h3>";
    echo "<ul>";
    foreach ($success as $item) {
        echo "<li>$item</li>";
    }
    echo "</ul>";
}

if (!empty($warnings)) {
    echo "<h3 style='color: orange;'>⚠️ Warnings</h3>";
    echo "<ul>";
    foreach ($warnings as $item) {
        echo "<li>$item</li>";
    }
    echo "</ul>";
}

if (!empty($errors)) {
    echo "<h3 style='color: red;'>❌ Errors</h3>";
    echo "<ul>";
    foreach ($errors as $item) {
        echo "<li>$item</li>";
    }
    echo "</ul>";
    
    echo "<h3>How to fix:</h3>";
    echo "<ol>";
    echo "<li>Make sure all files are uploaded correctly</li>";
    echo "<li>Check database credentials in config/database.php</li>";
    echo "<li>Import database schema from database/astrogenix_complete.sql</li>";
    echo "<li>Set proper file permissions (755 for directories, 644 for files)</li>";
    echo "<li>Install missing PHP extensions</li>";
    echo "</ol>";
} else {
    echo "<h2 style='color: green;'>🎉 All checks passed! System is ready.</h2>";
    echo "<p><a href='index.php'>Go to main page</a> | <a href='login.php'>Login</a> | <a href='register.php'>Register</a></p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>AstroGenix System Check</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: #f8fafc;
        }
        h1 { 
            color: #1e3a8a; 
            text-align: center;
            margin-bottom: 30px;
        }
        h2 { color: #374151; }
        h3 { margin-top: 25px; }
        ul { 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        ol {
            background: #fef3c7;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #f59e0b;
        }
        li { margin-bottom: 5px; }
        a {
            color: #1e3a8a;
            text-decoration: none;
            font-weight: bold;
        }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
</body>
</html>
