<?php
/**
 * AstroGenix - Simple Index Page
 * Simplified version without complex dependencies
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Simple database connection
try {
    $pdo = new PDO('mysql:host=localhost;dbname=astrogenix;charset=utf8mb4', 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    // Get investment packages
    $packages = $pdo->query("SELECT * FROM investment_packages WHERE is_active = 1 ORDER BY min_amount ASC")->fetchAll();
    
    // Get news
    $news = $pdo->query("SELECT * FROM news WHERE is_published = 1 ORDER BY created_at DESC LIMIT 3")->fetchAll();
    
} catch (Exception $e) {
    $packages = [];
    $news = [];
    $db_error = $e->getMessage();
}

// Simple translation function
function t($key) {
    $translations = [
        'welcome' => 'Welcome to AstroGenix',
        'nav_home' => 'Home',
        'nav_login' => 'Login', 
        'nav_register' => 'Register',
        'investment_packages' => 'Investment Packages',
        'daily_profit' => 'Daily Profit',
        'duration' => 'Duration',
        'days' => 'Days',
        'min_amount' => 'Min Amount',
        'max_amount' => 'Max Amount',
        'invest_now' => 'Invest Now',
        'latest_news' => 'Latest News',
        'footer_copyright' => '© 2024 AstroGenix. All rights reserved.'
    ];
    
    return isset($translations[$key]) ? $translations[$key] : $key;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AstroGenix - USDT Staking Platform</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1e3a8a;
            --secondary-color: #3b82f6;
            --accent-color: #10b981;
            --dark-color: #1f2937;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .navbar {
            background: rgba(30, 58, 138, 0.95) !important;
            backdrop-filter: blur(10px);
        }
        
        .hero-section {
            background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url('assets/images/crypto-bg.jpg');
            background-size: cover;
            background-position: center;
            color: white;
            padding: 100px 0;
            text-align: center;
        }
        
        .package-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .package-card:hover {
            transform: translateY(-10px);
        }
        
        .btn-invest {
            background: var(--accent-color);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-invest:hover {
            background: #059669;
            transform: scale(1.05);
        }
        
        .footer {
            background: var(--dark-color);
            color: white;
            padding: 40px 0;
            margin-top: 50px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-rocket me-2"></i>AstroGenix
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php"><?php echo t('nav_home'); ?></a>
                <a class="nav-link" href="login.php"><?php echo t('nav_login'); ?></a>
                <a class="nav-link" href="register.php"><?php echo t('nav_register'); ?></a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <h1 class="display-4 fw-bold mb-4"><?php echo t('welcome'); ?></h1>
            <p class="lead mb-4">Secure USDT Staking Platform with Daily Returns</p>
            <a href="register.php" class="btn btn-light btn-lg px-5">Get Started</a>
        </div>
    </section>

    <!-- Investment Packages -->
    <section class="py-5">
        <div class="container">
            <h2 class="text-center mb-5 text-white"><?php echo t('investment_packages'); ?></h2>
            
            <?php if (isset($db_error)): ?>
                <div class="alert alert-warning">
                    Database connection issue. Please check configuration.
                </div>
            <?php endif; ?>
            
            <div class="row">
                <?php if (!empty($packages)): ?>
                    <?php foreach ($packages as $package): ?>
                        <div class="col-md-4">
                            <div class="package-card text-center">
                                <h4 class="text-primary mb-3"><?php echo htmlspecialchars($package['name']); ?></h4>
                                <div class="mb-3">
                                    <span class="h2 text-success"><?php echo $package['daily_percentage']; ?>%</span>
                                    <small class="text-muted d-block"><?php echo t('daily_profit'); ?></small>
                                </div>
                                <div class="mb-3">
                                    <strong><?php echo $package['duration']; ?> <?php echo t('days'); ?></strong>
                                    <small class="text-muted d-block"><?php echo t('duration'); ?></small>
                                </div>
                                <div class="mb-4">
                                    <div><?php echo t('min_amount'); ?>: $<?php echo number_format($package['min_amount'], 2); ?></div>
                                    <div><?php echo t('max_amount'); ?>: $<?php echo number_format($package['max_amount'], 2); ?></div>
                                </div>
                                <a href="register.php" class="btn btn-invest w-100"><?php echo t('invest_now'); ?></a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12">
                        <div class="alert alert-info text-center">
                            No investment packages available at the moment.
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- News Section -->
    <?php if (!empty($news)): ?>
    <section class="py-5 bg-light">
        <div class="container">
            <h2 class="text-center mb-5"><?php echo t('latest_news'); ?></h2>
            <div class="row">
                <?php foreach ($news as $article): ?>
                    <div class="col-md-4">
                        <div class="card mb-4">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo htmlspecialchars($article['title_en']); ?></h5>
                                <p class="card-text"><?php echo substr(htmlspecialchars($article['content_en']), 0, 150) . '...'; ?></p>
                                <small class="text-muted"><?php echo date('M d, Y', strtotime($article['created_at'])); ?></small>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Footer -->
    <footer class="footer">
        <div class="container text-center">
            <p class="mb-0"><?php echo t('footer_copyright'); ?></p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
