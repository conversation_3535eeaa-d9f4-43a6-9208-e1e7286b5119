<?php
/**
 * AstroGenix - Function Test
 * Test all functions without conflicts
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>AstroGenix Function Test</h1>";

try {
    // Test basic PHP
    echo "<p>✅ PHP is working</p>";
    
    // Test session
    session_start();
    echo "<p>✅ Session started</p>";
    
    // Test database connection directly
    $dsn = "mysql:host=localhost;dbname=astrogenix;charset=utf8mb4";
    $pdo = new PDO($dsn, 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    echo "<p>✅ Database connection works</p>";
    
    // Test basic query
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "<p>✅ Database query works: {$result['count']} users</p>";
    
    // Now try to include config step by step
    echo "<h2>Testing Configuration Loading:</h2>";
    
    // Define ROOT_PATH first
    define('ROOT_PATH', __DIR__);
    echo "<p>✅ ROOT_PATH defined</p>";
    
    // Test database.php
    require_once 'config/database.php';
    echo "<p>✅ Database config loaded</p>";
    
    // Test security.php
    require_once 'includes/security.php';
    echo "<p>✅ Security functions loaded</p>";
    
    // Test language.php
    require_once 'includes/language.php';
    echo "<p>✅ Language functions loaded</p>";
    
    // Test functions.php
    require_once 'includes/functions.php';
    echo "<p>✅ Core functions loaded</p>";
    
    // Test auth.php
    require_once 'includes/auth.php';
    echo "<p>✅ Auth functions loaded</p>";
    
    // Test some functions
    if (function_exists('generateCSRFToken')) {
        $token = generateCSRFToken();
        echo "<p>✅ CSRF token generated: " . substr($token, 0, 10) . "...</p>";
    }
    
    if (function_exists('getSetting')) {
        $siteName = getSetting('site_name', 'AstroGenix');
        echo "<p>✅ Settings function works: Site name = $siteName</p>";
    }
    
    if (function_exists('getInvestmentPackages')) {
        $packages = getInvestmentPackages();
        echo "<p>✅ Investment packages function works: " . count($packages) . " packages found</p>";
    }
    
    echo "<h2 style='color: green;'>🎉 All functions loaded successfully!</h2>";
    echo "<p><a href='index.php'>Go to main page</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ Error:</h2>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>AstroGenix Function Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: #f8fafc;
        }
        h1 { 
            color: #1e3a8a; 
            text-align: center;
        }
        h2 { color: #374151; }
        p { margin: 10px 0; }
        a {
            color: #1e3a8a;
            text-decoration: none;
            font-weight: bold;
        }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
</body>
</html>
