<?php
/**
 * AstroGenix - Main Landing Page
 * Modern crypto staking platform homepage
 */

require_once 'config/config.php';

// Initialize variables
$packages = [];
$news = [];

try {
    // Redirect to dashboard if already logged in
    if (function_exists('isLoggedIn') && isLoggedIn()) {
        redirect('/pages/dashboard.php');
    }

    // Get investment packages for display
    if (function_exists('getInvestmentPackages')) {
        $packages = getInvestmentPackages(true);
    }

    // Get latest news
    if (function_exists('fetchAll')) {
        $news = fetchAll("SELECT * FROM news WHERE is_published = 1 ORDER BY created_at DESC LIMIT 3");
    }
} catch (Exception $e) {
    error_log("Index page error: " . $e->getMessage());
    // Continue with empty arrays
}

// Fallback function for translations if not available
if (!function_exists('t')) {
    function t($key, $default = null) {
        // Simple fallback translations
        $translations = [
            'welcome' => 'Welcome',
            'nav_home' => 'Home',
            'nav_login' => 'Login',
            'nav_register' => 'Register',
            'investment_packages' => 'Investment Packages',
            'footer_about' => 'About',
            'footer_contact' => 'Contact',
            'footer_copyright' => '© 2024 AstroGenix. All rights reserved.',
            'daily_profit' => 'Daily Profit',
            'duration' => 'Duration',
            'days' => 'Days',
            'total_return' => 'Total Return',
            'min_amount' => 'Min Amount',
            'max_amount' => 'Max Amount',
            'invest_now' => 'Invest Now'
        ];

        return isset($translations[$key]) ? $translations[$key] : ($default !== null ? $default : $key);
    }
}
?>
<!DOCTYPE html>
<html lang="<?php echo isset($_SESSION['language']) ? $_SESSION['language'] : 'en'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo t('welcome'); ?> - <?php echo SITE_NAME; ?></title>
    <meta name="description" content="<?php echo getSetting('site_description', 'Professional USDT Staking Platform'); ?>">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="/assets/css/main.css" as="style">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" as="style">
    
    <!-- Stylesheets -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="/assets/css/main.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
    
    <!-- Meta tags for SEO -->
    <meta name="keywords" content="USDT staking, crypto investment, passive income, blockchain">
    <meta name="author" content="AstroGenix">
    <meta property="og:title" content="AstroGenix - Professional USDT Staking Platform">
    <meta property="og:description" content="Earn passive income with our secure USDT staking platform">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL; ?>">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <div class="logo">
                    <a href="/"><?php echo SITE_NAME; ?></a>
                </div>
                
                <ul class="nav-menu">
                    <li><a href="#home" class="nav-link"><?php echo t('nav_home'); ?></a></li>
                    <li><a href="#packages" class="nav-link"><?php echo t('investment_packages'); ?></a></li>
                    <li><a href="#about" class="nav-link"><?php echo t('footer_about'); ?></a></li>
                    <li><a href="#contact" class="nav-link"><?php echo t('footer_contact'); ?></a></li>
                </ul>
                
                <div class="nav-actions">
                    <a href="/login.php" class="btn btn-outline"><?php echo t('nav_login'); ?></a>
                    <a href="/register.php" class="btn btn-primary"><?php echo t('nav_register'); ?></a>
                    
                    <!-- Language switcher -->
                    <div class="language-switcher">
                        <select onchange="changeLanguage(this.value)" class="form-control" style="width: auto; display: inline-block;">
                            <option value="en" <?php echo (!isset($_SESSION['language']) || $_SESSION['language'] === 'en') ? 'selected' : ''; ?>>EN</option>
                            <option value="ru" <?php echo (isset($_SESSION['language']) && $_SESSION['language'] === 'ru') ? 'selected' : ''; ?>>RU</option>
                        </select>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="row align-center">
                    <div class="col-6">
                        <h1 class="hero-title fade-in-up">
                            <?php echo t('welcome'); ?> to <span class="gradient-text"><?php echo SITE_NAME; ?></span>
                        </h1>
                        <p class="hero-subtitle fade-in-up fade-in-delay-1">
                            The most advanced USDT staking platform. Earn passive income with our secure and profitable investment packages.
                        </p>
                        <div class="hero-stats fade-in-up fade-in-delay-2">
                            <div class="stat-item hover-lift">
                                <div class="stat-number">$2.5M+</div>
                                <div class="stat-label">Total Staked</div>
                            </div>
                            <div class="stat-item hover-lift">
                                <div class="stat-number">15,000+</div>
                                <div class="stat-label">Active Users</div>
                            </div>
                            <div class="stat-item hover-lift">
                                <div class="stat-number">99.9%</div>
                                <div class="stat-label">Uptime</div>
                            </div>
                        </div>
                        <div class="hero-actions fade-in-up fade-in-delay-3">
                            <a href="/register.php" class="btn btn-primary btn-lg hover-glow"><?php echo t('nav_register'); ?></a>
                            <a href="#packages" class="btn btn-outline btn-lg hover-lift">View Packages</a>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="hero-visual fade-in-up fade-in-delay-4">
                            <div class="crypto-animation float">
                                <!-- Modern crypto visual element -->
                                <div class="crypto-icon">₿</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Investment Packages Section -->
    <section id="packages" class="packages-section">
        <div class="container">
            <div class="section-header text-center">
                <h2><?php echo t('investment_packages'); ?></h2>
                <p>Choose the perfect staking plan for your investment goals</p>
            </div>
            
            <div class="packages-grid">
                <?php $delay = 1; foreach ($packages as $package): ?>
                    <?php $profits = calculateInvestmentProfit($package['min_amount'], $package['daily_percentage'], $package['duration_days']); ?>
                    <div class="package-card card fade-in-up fade-in-delay-<?php echo $delay; ?> hover-lift">
                        <div class="card-header">
                            <h3 class="card-title"><?php echo htmlspecialchars($package['name']); ?></h3>
                            <p class="card-subtitle"><?php echo htmlspecialchars($package['description']); ?></p>
                        </div>
                        
                        <div class="card-body">
                            <div class="package-stats">
                                <div class="stat">
                                    <span class="stat-label"><?php echo t('daily_profit'); ?></span>
                                    <span class="stat-value"><?php echo formatPercentage($package['daily_percentage']); ?></span>
                                </div>
                                <div class="stat">
                                    <span class="stat-label"><?php echo t('duration'); ?></span>
                                    <span class="stat-value"><?php echo $package['duration_days']; ?> <?php echo t('days'); ?></span>
                                </div>
                                <div class="stat">
                                    <span class="stat-label"><?php echo t('total_return'); ?></span>
                                    <span class="stat-value"><?php echo formatPercentage($package['total_return_percentage']); ?></span>
                                </div>
                            </div>
                            
                            <div class="package-range">
                                <span class="range-label"><?php echo t('min_amount'); ?>:</span>
                                <span class="range-value">$<?php echo formatCurrency($package['min_amount'], 0); ?></span>
                            </div>
                            <div class="package-range">
                                <span class="range-label"><?php echo t('max_amount'); ?>:</span>
                                <span class="range-value">$<?php echo formatCurrency($package['max_amount'], 0); ?></span>
                            </div>
                        </div>
                        
                        <div class="card-footer">
                            <a href="/register.php" class="btn btn-primary w-full hover-glow"><?php echo t('invest_now'); ?></a>
                        </div>
                    </div>
                <?php $delay++; endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
        <div class="container">
            <div class="section-header text-center">
                <h2>Why Choose <?php echo SITE_NAME; ?>?</h2>
                <p>Advanced features for professional crypto staking</p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card card fade-in-up fade-in-delay-1 hover-lift">
                    <div class="feature-icon">🔒</div>
                    <h3>Bank-Level Security</h3>
                    <p>Your funds are protected with military-grade encryption and multi-signature wallets.</p>
                </div>

                <div class="feature-card card fade-in-up fade-in-delay-2 hover-lift">
                    <div class="feature-icon">💰</div>
                    <h3>High Returns</h3>
                    <p>Earn up to 3% daily returns with our optimized staking strategies.</p>
                </div>

                <div class="feature-card card fade-in-up fade-in-delay-3 hover-lift">
                    <div class="feature-icon">⚡</div>
                    <h3>Instant Payouts</h3>
                    <p>Receive your daily profits automatically with our advanced payout system.</p>
                </div>

                <div class="feature-card card fade-in-up fade-in-delay-4 hover-lift">
                    <div class="feature-icon">🌐</div>
                    <h3>Global Access</h3>
                    <p>Access your investments from anywhere in the world, 24/7.</p>
                </div>

                <div class="feature-card card fade-in-up fade-in-delay-5 hover-lift">
                    <div class="feature-icon">📱</div>
                    <h3>Mobile Optimized</h3>
                    <p>Full-featured mobile experience for trading on the go.</p>
                </div>

                <div class="feature-card card fade-in-up fade-in-delay-1 hover-lift">
                    <div class="feature-icon">🎯</div>
                    <h3>Referral Program</h3>
                    <p>Earn additional income by referring friends to our platform.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- News Section -->
    <?php if (!empty($news)): ?>
    <section class="news-section">
        <div class="container">
            <div class="section-header text-center">
                <h2>Latest News</h2>
                <p>Stay updated with the latest developments</p>
            </div>
            
            <div class="news-grid">
                <?php foreach ($news as $article): ?>
                    <div class="news-card card fade-in">
                        <?php if ($article['image_url']): ?>
                            <div class="news-image">
                                <img src="<?php echo htmlspecialchars($article['image_url']); ?>" alt="News Image">
                            </div>
                        <?php endif; ?>
                        
                        <div class="news-content">
                            <h3><?php echo htmlspecialchars($article['title_' . $_SESSION['language']]); ?></h3>
                            <p><?php echo substr(htmlspecialchars($article['content_' . $_SESSION['language']]), 0, 150) . '...'; ?></p>
                            <div class="news-meta">
                                <span class="news-date"><?php echo date('M j, Y', strtotime($article['created_at'])); ?></span>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="row">
                    <div class="col-4">
                        <div class="footer-section">
                            <h3><?php echo SITE_NAME; ?></h3>
                            <p>The most advanced USDT staking platform for earning passive income through secure crypto investments.</p>
                        </div>
                    </div>
                    
                    <div class="col-2">
                        <div class="footer-section">
                            <h4>Platform</h4>
                            <ul class="footer-links">
                                <li><a href="#packages">Investment Plans</a></li>
                                <li><a href="/register.php">Get Started</a></li>
                                <li><a href="#about">About Us</a></li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="col-2">
                        <div class="footer-section">
                            <h4>Support</h4>
                            <ul class="footer-links">
                                <li><a href="#contact">Contact Us</a></li>
                                <li><a href="#">FAQ</a></li>
                                <li><a href="#">Help Center</a></li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="col-2">
                        <div class="footer-section">
                            <h4>Legal</h4>
                            <ul class="footer-links">
                                <li><a href="#">Terms of Service</a></li>
                                <li><a href="#">Privacy Policy</a></li>
                                <li><a href="#">Risk Disclosure</a></li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="col-2">
                        <div class="footer-section">
                            <h4>Connect</h4>
                            <div class="social-links">
                                <a href="#" class="social-link">📱 Telegram</a>
                                <a href="#" class="social-link">🐦 Twitter</a>
                                <a href="#" class="social-link">📧 Email</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <div class="row justify-between align-center">
                    <div class="col">
                        <p><?php echo t('footer_copyright'); ?></p>
                    </div>
                    <div class="col text-right">
                        <p>Powered by AstroGenix Technology</p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="/assets/js/main.js"></script>
    <script>
        // Language switcher
        function changeLanguage(lang) {
            fetch('/api/language.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ language: lang })
            }).then(() => {
                location.reload();
            });
        }
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
