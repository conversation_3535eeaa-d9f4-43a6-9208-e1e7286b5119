<?php
/**
 * AstroGenix - User Dashboard
 * Main user control panel
 */

require_once '../config/config.php';

// Require authentication
requireAuth();

// Get current user data
$user = getCurrentUser();
if (!$user) {
    redirect('/login.php');
}

// Get user statistics
$userStats = [
    'balance' => $user['balance_usdt'],
    'total_invested' => $user['total_invested'],
    'total_earned' => $user['total_earned'],
    'referral_earnings' => $user['referral_earnings']
];

// Get active investments
$activeInvestments = getUserInvestments($user['id'], 'active');

// Get recent transactions
$recentTransactions = getUserTransactions($user['id'], 10);

// Get investment packages
$packages = getInvestmentPackages(true);

// Get referral statistics
$referralStats = getReferralStats($user['id']);

// Calculate total active investment amount
$totalActiveInvestment = 0;
foreach ($activeInvestments as $investment) {
    $totalActiveInvestment += $investment['amount'];
}
?>
<!DOCTYPE html>
<html lang="<?php echo $_SESSION['language']; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo t('dashboard_title'); ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Stylesheets -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="/assets/css/main.css" rel="stylesheet">
    
    <style>
        .dashboard-container {
            min-height: 100vh;
            background: var(--bg-primary);
        }
        
        .sidebar {
            width: 250px;
            background: var(--bg-secondary);
            border-right: 1px solid var(--border-color);
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
        }
        
        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }
        
        .sidebar-logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin-bottom: 0.25rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .nav-link:hover,
        .nav-link.active {
            color: var(--text-primary);
            background: var(--bg-tertiary);
            border-left-color: var(--primary-light);
        }
        
        .nav-icon {
            margin-right: 0.75rem;
            font-size: 1.125rem;
        }
        
        .main-content {
            margin-left: 250px;
            padding: 2rem;
        }
        
        .top-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1rem;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 1rem;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--gradient-primary);
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .stat-title {
            font-size: 0.875rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .stat-icon {
            font-size: 1.5rem;
            opacity: 0.7;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .stat-change {
            font-size: 0.875rem;
            color: var(--success-color);
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
        }
        
        .investments-section,
        .transactions-section {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 1rem;
            padding: 1.5rem;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .investment-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: var(--bg-tertiary);
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .investment-info h4 {
            margin-bottom: 0.25rem;
            color: var(--text-primary);
        }
        
        .investment-info p {
            margin: 0;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
        
        .investment-stats {
            text-align: right;
        }
        
        .investment-amount {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--primary-light);
        }
        
        .investment-progress {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
        
        .transaction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .transaction-item:last-child {
            border-bottom: none;
        }
        
        .transaction-info {
            flex: 1;
        }
        
        .transaction-type {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }
        
        .transaction-date {
            font-size: 0.75rem;
            color: var(--text-muted);
        }
        
        .transaction-amount {
            font-weight: 600;
            text-align: right;
        }
        
        .amount-positive {
            color: var(--success-color);
        }
        
        .amount-negative {
            color: var(--error-color);
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .action-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .action-card:hover {
            transform: translateY(-2px);
            border-color: var(--primary-light);
        }
        
        .action-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            display: block;
        }
        
        .mobile-menu-toggle {
            display: none;
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1001;
            background: var(--primary-light);
            color: white;
            border: none;
            border-radius: 0.5rem;
            padding: 0.5rem;
            cursor: pointer;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
                padding: 1rem;
            }
            
            .mobile-menu-toggle {
                display: block;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .quick-actions {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Mobile menu toggle -->
        <button class="mobile-menu-toggle" onclick="toggleSidebar()">
            ☰
        </button>
        
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo"><?php echo SITE_NAME; ?></div>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-item">
                    <a href="/pages/dashboard.php" class="nav-link active">
                        <span class="nav-icon">📊</span>
                        <?php echo t('nav_dashboard'); ?>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/pages/invest.php" class="nav-link">
                        <span class="nav-icon">💰</span>
                        <?php echo t('nav_invest'); ?>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/pages/referrals.php" class="nav-link">
                        <span class="nav-icon">👥</span>
                        <?php echo t('nav_referrals'); ?>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/pages/tasks.php" class="nav-link">
                        <span class="nav-icon">✅</span>
                        <?php echo t('nav_tasks'); ?>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/pages/support.php" class="nav-link">
                        <span class="nav-icon">💬</span>
                        <?php echo t('nav_support'); ?>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/pages/profile.php" class="nav-link">
                        <span class="nav-icon">👤</span>
                        <?php echo t('nav_profile'); ?>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/logout.php" class="nav-link">
                        <span class="nav-icon">🚪</span>
                        <?php echo t('nav_logout'); ?>
                    </a>
                </div>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Bar -->
            <div class="top-bar">
                <div class="user-info">
                    <div class="user-avatar">
                        <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                    </div>
                    <div>
                        <h3><?php echo t('welcome'); ?>, <?php echo htmlspecialchars($user['first_name']); ?>!</h3>
                        <p>Last login: <?php echo $user['last_login'] ? date('M j, Y H:i', strtotime($user['last_login'])) : 'First time'; ?></p>
                    </div>
                </div>
                
                <div class="top-actions">
                    <select onchange="changeLanguage(this.value)" class="form-control" style="width: auto; display: inline-block;">
                        <option value="en" <?php echo $_SESSION['language'] === 'en' ? 'selected' : ''; ?>>EN</option>
                        <option value="ru" <?php echo $_SESSION['language'] === 'ru' ? 'selected' : ''; ?>>RU</option>
                    </select>
                </div>
            </div>
            
            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title"><?php echo t('total_balance'); ?></span>
                        <span class="stat-icon">💰</span>
                    </div>
                    <div class="stat-value">$<?php echo formatCurrency($userStats['balance']); ?></div>
                    <div class="stat-change">Available for investment</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title"><?php echo t('total_invested'); ?></span>
                        <span class="stat-icon">📈</span>
                    </div>
                    <div class="stat-value">$<?php echo formatCurrency($userStats['total_invested']); ?></div>
                    <div class="stat-change">Active: $<?php echo formatCurrency($totalActiveInvestment); ?></div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title"><?php echo t('total_earned'); ?></span>
                        <span class="stat-icon">💎</span>
                    </div>
                    <div class="stat-value">$<?php echo formatCurrency($userStats['total_earned']); ?></div>
                    <div class="stat-change">Profit earned</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title"><?php echo t('referral_earnings'); ?></span>
                        <span class="stat-icon">👥</span>
                    </div>
                    <div class="stat-value">$<?php echo formatCurrency($userStats['referral_earnings']); ?></div>
                    <div class="stat-change"><?php echo $referralStats['total_referrals']; ?> referrals</div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="/pages/invest.php" class="action-card">
                    <span class="action-icon">💰</span>
                    <h4><?php echo t('invest_now'); ?></h4>
                    <p>Start earning today</p>
                </a>
                
                <a href="/pages/deposit.php" class="action-card">
                    <span class="action-icon">📥</span>
                    <h4><?php echo t('deposit'); ?></h4>
                    <p>Add funds to account</p>
                </a>
                
                <a href="/pages/withdraw.php" class="action-card">
                    <span class="action-icon">📤</span>
                    <h4><?php echo t('withdrawal'); ?></h4>
                    <p>Withdraw your profits</p>
                </a>
                
                <a href="/pages/referrals.php" class="action-card">
                    <span class="action-icon">🔗</span>
                    <h4>Refer Friends</h4>
                    <p>Earn commissions</p>
                </a>
            </div>
            
            <!-- Dashboard Grid -->
            <div class="dashboard-grid">
                <!-- Active Investments -->
                <div class="investments-section">
                    <div class="section-header">
                        <h3 class="section-title"><?php echo t('active_investments'); ?></h3>
                        <a href="/pages/invest.php" class="btn btn-primary">New Investment</a>
                    </div>
                    
                    <?php if (empty($activeInvestments)): ?>
                        <div class="text-center p-4">
                            <p class="text-muted">No active investments yet.</p>
                            <a href="/pages/invest.php" class="btn btn-primary">Start Investing</a>
                        </div>
                    <?php else: ?>
                        <?php foreach ($activeInvestments as $investment): ?>
                            <div class="investment-item">
                                <div class="investment-info">
                                    <h4><?php echo htmlspecialchars($investment['package_name']); ?></h4>
                                    <p>Daily: <?php echo formatPercentage($investment['daily_percentage']); ?> | 
                                       Progress: <?php echo $investment['days_completed']; ?>/<?php echo $investment['total_days']; ?> days</p>
                                </div>
                                <div class="investment-stats">
                                    <div class="investment-amount">$<?php echo formatCurrency($investment['amount']); ?></div>
                                    <div class="investment-progress">
                                        Earned: $<?php echo formatCurrency($investment['total_profit']); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                
                <!-- Recent Transactions -->
                <div class="transactions-section">
                    <div class="section-header">
                        <h3 class="section-title"><?php echo t('recent_transactions'); ?></h3>
                        <a href="/pages/transactions.php" class="btn btn-outline">View All</a>
                    </div>
                    
                    <?php if (empty($recentTransactions)): ?>
                        <div class="text-center p-4">
                            <p class="text-muted">No transactions yet.</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($recentTransactions as $transaction): ?>
                            <div class="transaction-item">
                                <div class="transaction-info">
                                    <div class="transaction-type">
                                        <?php echo ucfirst(str_replace('_', ' ', $transaction['type'])); ?>
                                    </div>
                                    <div class="transaction-date">
                                        <?php echo date('M j, Y H:i', strtotime($transaction['created_at'])); ?>
                                    </div>
                                </div>
                                <div class="transaction-amount <?php echo in_array($transaction['type'], ['deposit', 'profit', 'referral', 'bonus']) ? 'amount-positive' : 'amount-negative'; ?>">
                                    <?php echo in_array($transaction['type'], ['deposit', 'profit', 'referral', 'bonus']) ? '+' : '-'; ?>$<?php echo formatCurrency($transaction['amount']); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Language switcher
        function changeLanguage(lang) {
            fetch('/api/language.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ language: lang })
            }).then(() => {
                location.reload();
            });
        }
        
        // Mobile sidebar toggle
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        }
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('sidebar');
            const toggle = document.querySelector('.mobile-menu-toggle');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(e.target) && 
                !toggle.contains(e.target) && 
                sidebar.classList.contains('open')) {
                sidebar.classList.remove('open');
            }
        });
        
        // Auto-refresh data every 30 seconds
        setInterval(function() {
            // You can implement AJAX refresh here
            console.log('Auto-refresh triggered');
        }, 30000);
    </script>
</body>
</html>
