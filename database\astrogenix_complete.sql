-- =====================================================
-- AstroGenix - Полная схема базы данных
-- Платформа для стейкинга USDT
-- Версия: 1.0.0
-- =====================================================

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- Установка кодировки
/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- Отключение проверки внешних ключей для корректного создания таблиц
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- СОЗДАНИЕ ТАБЛИЦ
-- =====================================================

-- Таблица пользователей
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `country` varchar(50) DEFAULT NULL,
  `balance_usdt` decimal(15,6) DEFAULT 0.000000,
  `total_invested` decimal(15,6) DEFAULT 0.000000,
  `total_earned` decimal(15,6) DEFAULT 0.000000,
  `referral_code` varchar(20) NOT NULL,
  `referred_by` int(11) DEFAULT NULL,
  `referral_earnings` decimal(15,6) DEFAULT 0.000000,
  `is_active` tinyint(1) DEFAULT 1,
  `is_verified` tinyint(1) DEFAULT 0,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `referral_code` (`referral_code`),
  KEY `referred_by` (`referred_by`),
  KEY `is_active` (`is_active`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Таблица инвестиционных пакетов
CREATE TABLE `investment_packages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `min_amount` decimal(15,6) NOT NULL,
  `max_amount` decimal(15,6) NOT NULL,
  `daily_percentage` decimal(5,2) NOT NULL,
  `duration_days` int(11) NOT NULL,
  `total_return_percentage` decimal(5,2) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `is_active` (`is_active`),
  KEY `sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Таблица инвестиций пользователей
CREATE TABLE `user_investments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `package_id` int(11) NOT NULL,
  `amount` decimal(15,6) NOT NULL,
  `daily_profit` decimal(15,6) NOT NULL,
  `total_profit` decimal(15,6) DEFAULT 0.000000,
  `days_completed` int(11) DEFAULT 0,
  `total_days` int(11) NOT NULL,
  `next_payout` date NOT NULL,
  `status` enum('active','completed','cancelled') DEFAULT 'active',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `package_id` (`package_id`),
  KEY `status` (`status`),
  KEY `next_payout` (`next_payout`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Таблица транзакций
CREATE TABLE `transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` enum('deposit','withdrawal','investment','profit','referral','bonus','task_reward') NOT NULL,
  `amount` decimal(15,6) NOT NULL,
  `description` varchar(255) NOT NULL,
  `reference_id` int(11) DEFAULT NULL,
  `status` enum('pending','completed','cancelled','rejected') DEFAULT 'pending',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `processed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Таблица депозитов
CREATE TABLE `deposits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `amount` decimal(15,6) NOT NULL,
  `payment_method` varchar(50) NOT NULL,
  `transaction_hash` varchar(255) DEFAULT NULL,
  `wallet_address` varchar(255) DEFAULT NULL,
  `status` enum('pending','confirmed','rejected') DEFAULT 'pending',
  `admin_notes` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `processed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Таблица выводов средств
CREATE TABLE `withdrawals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `amount` decimal(15,6) NOT NULL,
  `wallet_address` varchar(255) NOT NULL,
  `network` varchar(50) NOT NULL,
  `status` enum('pending','processing','completed','rejected') DEFAULT 'pending',
  `transaction_hash` varchar(255) DEFAULT NULL,
  `admin_notes` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `processed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Таблица реферальных комиссий
CREATE TABLE `referral_commissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `referrer_id` int(11) NOT NULL,
  `referred_id` int(11) NOT NULL,
  `level` int(11) NOT NULL,
  `amount` decimal(15,6) NOT NULL,
  `percentage` decimal(5,2) NOT NULL,
  `source_type` enum('investment','deposit') NOT NULL,
  `source_amount` decimal(15,6) NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `referrer_id` (`referrer_id`),
  KEY `referred_id` (`referred_id`),
  KEY `level` (`level`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Таблица заданий
CREATE TABLE `tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `description` text NOT NULL,
  `reward_amount` decimal(15,6) NOT NULL,
  `task_type` enum('daily','social','referral','investment') NOT NULL,
  `requirements` text,
  `is_active` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `is_active` (`is_active`),
  KEY `task_type` (`task_type`),
  KEY `sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Таблица выполненных заданий пользователей
CREATE TABLE `user_tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `task_id` int(11) NOT NULL,
  `status` enum('pending','completed','claimed') DEFAULT 'pending',
  `completed_at` timestamp NULL DEFAULT NULL,
  `claimed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_task` (`user_id`,`task_id`),
  KEY `user_id` (`user_id`),
  KEY `task_id` (`task_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Таблица новостей
CREATE TABLE `news` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title_en` varchar(255) NOT NULL,
  `title_ru` varchar(255) NOT NULL,
  `content_en` text NOT NULL,
  `content_ru` text NOT NULL,
  `image_url` varchar(255) DEFAULT NULL,
  `is_published` tinyint(1) DEFAULT 1,
  `is_featured` tinyint(1) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `is_published` (`is_published`),
  KEY `is_featured` (`is_featured`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Таблица тикетов поддержки
CREATE TABLE `support_tickets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `category` enum('general','technical','financial','account') NOT NULL,
  `priority` enum('low','medium','high','urgent') DEFAULT 'medium',
  `status` enum('open','in_progress','resolved','closed') DEFAULT 'open',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `category` (`category`),
  KEY `priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Таблица сообщений поддержки
CREATE TABLE `support_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ticket_id` int(11) NOT NULL,
  `sender_type` enum('user','admin') NOT NULL,
  `sender_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `attachments` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `ticket_id` (`ticket_id`),
  KEY `sender_type` (`sender_type`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Таблица администраторов
CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('super_admin','admin','moderator') DEFAULT 'admin',
  `permissions` text,
  `is_active` tinyint(1) DEFAULT 1,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `role` (`role`),
  KEY `is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Таблица настроек сайта
CREATE TABLE `site_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `setting_type` enum('text','number','boolean','json') DEFAULT 'text',
  `description` varchar(255),
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Таблица логов активности
CREATE TABLE `activity_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `details` text,
  `ip_address` varchar(45),
  `user_agent` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `action` (`action`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- ДОБАВЛЕНИЕ ВНЕШНИХ КЛЮЧЕЙ
-- =====================================================

-- Внешние ключи для таблицы users
ALTER TABLE `users`
  ADD CONSTRAINT `users_referred_by_fk` FOREIGN KEY (`referred_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

-- Внешние ключи для таблицы user_investments
ALTER TABLE `user_investments`
  ADD CONSTRAINT `user_investments_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_investments_package_fk` FOREIGN KEY (`package_id`) REFERENCES `investment_packages` (`id`) ON DELETE CASCADE;

-- Внешние ключи для таблицы transactions
ALTER TABLE `transactions`
  ADD CONSTRAINT `transactions_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

-- Внешние ключи для таблицы deposits
ALTER TABLE `deposits`
  ADD CONSTRAINT `deposits_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

-- Внешние ключи для таблицы withdrawals
ALTER TABLE `withdrawals`
  ADD CONSTRAINT `withdrawals_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

-- Внешние ключи для таблицы referral_commissions
ALTER TABLE `referral_commissions`
  ADD CONSTRAINT `referral_commissions_referrer_fk` FOREIGN KEY (`referrer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `referral_commissions_referred_fk` FOREIGN KEY (`referred_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

-- Внешние ключи для таблицы user_tasks
ALTER TABLE `user_tasks`
  ADD CONSTRAINT `user_tasks_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_tasks_task_fk` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE;

-- Внешние ключи для таблицы support_tickets
ALTER TABLE `support_tickets`
  ADD CONSTRAINT `support_tickets_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

-- Внешние ключи для таблицы support_messages
ALTER TABLE `support_messages`
  ADD CONSTRAINT `support_messages_ticket_fk` FOREIGN KEY (`ticket_id`) REFERENCES `support_tickets` (`id`) ON DELETE CASCADE;

-- Внешние ключи для таблицы activity_logs
ALTER TABLE `activity_logs`
  ADD CONSTRAINT `activity_logs_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

-- =====================================================
-- ВСТАВКА НАЧАЛЬНЫХ ДАННЫХ
-- =====================================================

-- Вставка администратора по умолчанию
-- Логин: admin, Пароль: admin123
INSERT INTO `admin_users` (`username`, `email`, `password`, `role`, `is_active`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'super_admin', 1);

-- Вставка инвестиционных пакетов
INSERT INTO `investment_packages` (`name`, `description`, `min_amount`, `max_amount`, `daily_percentage`, `duration_days`, `total_return_percentage`, `is_active`, `sort_order`) VALUES
('Starter Plan', 'Идеально подходит для начинающих в криптостейкинге', 10.000000, 999.999999, 1.50, 30, 45.00, 1, 1),
('Professional Plan', 'Продвинутый стейкинг с высокой доходностью', 1000.000000, 4999.999999, 2.00, 45, 90.00, 1, 2),
('VIP Plan', 'Премиум стейкинг для серьезных инвесторов', 5000.000000, 19999.999999, 2.50, 60, 150.00, 1, 3),
('Elite Plan', 'Максимальная доходность для элитных инвесторов', 20000.000000, 99999.999999, 3.00, 90, 270.00, 1, 4);

-- Вставка заданий
INSERT INTO `tasks` (`title`, `description`, `reward_amount`, `task_type`, `requirements`, `is_active`, `sort_order`) VALUES
('Приветственный бонус', 'Завершите профиль и подтвердите email', 5.000000, 'daily', 'Завершить верификацию профиля', 1, 1),
('Ежедневный вход', 'Заходите в аккаунт каждый день', 0.500000, 'daily', 'Войти в систему один раз в день', 1, 2),
('Первая инвестиция', 'Сделайте свою первую инвестицию', 10.000000, 'investment', 'Инвестировать минимум $10', 1, 3),
('Пригласить друга', 'Пригласите друзей присоединиться к AstroGenix', 25.000000, 'referral', 'Успешная регистрация реферала', 1, 4),
('Подписка в соцсетях', 'Подпишитесь на нас в социальных сетях', 2.000000, 'social', 'Подписаться в Twitter и Telegram', 1, 5);

-- Вставка настроек сайта
INSERT INTO `site_settings` (`setting_key`, `setting_value`, `setting_type`, `description`) VALUES
('site_name', 'AstroGenix', 'text', 'Название сайта'),
('site_description', 'Профессиональная платформа для стейкинга USDT', 'text', 'Описание сайта'),
('registration_bonus', '10.00', 'number', 'Бонус за регистрацию'),
('min_withdrawal', '5.00', 'number', 'Минимальная сумма вывода'),
('max_withdrawal', '10000.00', 'number', 'Максимальная сумма вывода'),
('withdrawal_fee', '1.00', 'number', 'Комиссия за вывод'),
('referral_levels', '3', 'number', 'Количество реферальных уровней'),
('referral_level_1', '10.00', 'number', 'Процент комиссии 1 уровня'),
('referral_level_2', '5.00', 'number', 'Процент комиссии 2 уровня'),
('referral_level_3', '2.00', 'number', 'Процент комиссии 3 уровня'),
('maintenance_mode', 'false', 'boolean', 'Режим технического обслуживания'),
('email_verification', 'false', 'boolean', 'Требовать подтверждение email'),
('kyc_verification', 'false', 'boolean', 'Требовать KYC верификацию'),
('telegram_bot_token', '', 'text', 'Токен Telegram бота для уведомлений'),
('telegram_chat_id', '', 'text', 'ID чата Telegram для админских уведомлений'),
('usdt_wallet_address', '', 'text', 'Адрес USDT кошелька для депозитов'),
('supported_networks', '["TRC20", "ERC20", "BEP20"]', 'json', 'Поддерживаемые блокчейн сети');

-- Вставка новостей
INSERT INTO `news` (`title_en`, `title_ru`, `content_en`, `content_ru`, `is_published`, `is_featured`) VALUES
('Welcome to AstroGenix', 'Добро пожаловать в AstroGenix',
'We are excited to launch AstroGenix, the most advanced USDT staking platform. Start earning passive income today with our secure and profitable investment packages. Our platform offers industry-leading security, competitive returns, and 24/7 customer support.',
'Мы рады представить AstroGenix - самую продвинутую платформу для стейкинга USDT. Начните зарабатывать пассивный доход уже сегодня с нашими безопасными и прибыльными инвестиционными пакетами. Наша платформа предлагает ведущую в отрасли безопасность, конкурентную доходность и круглосуточную поддержку клиентов.',
1, 1),

('New Investment Packages Available', 'Доступны новые инвестиционные пакеты',
'We have added new investment packages with higher returns and flexible terms. Check out our VIP and Elite plans for maximum profitability. These packages are designed for serious investors who want to maximize their USDT staking returns.',
'Мы добавили новые инвестиционные пакеты с более высокой доходностью и гибкими условиями. Ознакомьтесь с нашими VIP и Elite планами для максимальной прибыльности. Эти пакеты разработаны для серьезных инвесторов, которые хотят максимизировать доходность от стейкинга USDT.',
1, 0),

('Enhanced Security Features', 'Улучшенные функции безопасности',
'We have implemented additional security measures to protect your investments. Your funds are now even more secure with our advanced encryption, multi-signature wallets, and real-time monitoring systems.',
'Мы внедрили дополнительные меры безопасности для защиты ваших инвестиций. Ваши средства теперь еще более защищены благодаря нашему продвинутому шифрованию, мультиподписным кошелькам и системам мониторинга в реальном времени.',
1, 0),

('Referral Program Launch', 'Запуск реферальной программы',
'Earn additional income by referring friends to AstroGenix! Our multi-level referral program offers up to 10% commission on first level, 5% on second level, and 2% on third level. Start sharing your referral link today.',
'Зарабатывайте дополнительный доход, приглашая друзей в AstroGenix! Наша многоуровневая реферальная программа предлагает до 10% комиссии с первого уровня, 5% со второго уровня и 2% с третьего уровня. Начните делиться своей реферальной ссылкой уже сегодня.',
1, 0);

-- Вставка тестового пользователя (опционально)
INSERT INTO `users` (`username`, `email`, `password`, `first_name`, `last_name`, `referral_code`, `balance_usdt`, `is_active`, `is_verified`) VALUES
('testuser', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Test', 'User', 'TEST123', 100.000000, 1, 1);

-- Включение проверки внешних ключей
SET FOREIGN_KEY_CHECKS = 1;

-- Автоинкремент для таблиц
ALTER TABLE `users` AUTO_INCREMENT = 2;
ALTER TABLE `investment_packages` AUTO_INCREMENT = 5;
ALTER TABLE `user_investments` AUTO_INCREMENT = 1;
ALTER TABLE `transactions` AUTO_INCREMENT = 1;
ALTER TABLE `deposits` AUTO_INCREMENT = 1;
ALTER TABLE `withdrawals` AUTO_INCREMENT = 1;
ALTER TABLE `referral_commissions` AUTO_INCREMENT = 1;
ALTER TABLE `tasks` AUTO_INCREMENT = 6;
ALTER TABLE `user_tasks` AUTO_INCREMENT = 1;
ALTER TABLE `news` AUTO_INCREMENT = 5;
ALTER TABLE `support_tickets` AUTO_INCREMENT = 1;
ALTER TABLE `support_messages` AUTO_INCREMENT = 1;
ALTER TABLE `admin_users` AUTO_INCREMENT = 2;
ALTER TABLE `site_settings` AUTO_INCREMENT = 17;
ALTER TABLE `activity_logs` AUTO_INCREMENT = 1;

COMMIT;

-- Восстановление настроек кодировки
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

-- =====================================================
-- ЗАВЕРШЕНИЕ УСТАНОВКИ
-- =====================================================

-- Информация о созданной базе данных
SELECT 'AstroGenix database successfully created!' as message;
SELECT 'Default admin login: admin / admin123' as admin_info;
SELECT 'Test user login: testuser / admin123' as test_user_info;
SELECT 'Please change default passwords after first login!' as security_warning;
