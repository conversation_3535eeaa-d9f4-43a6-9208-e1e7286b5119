# AstroGenix - Инструкция по установке

## 🚀 Быстрая установка

### 1. Создание базы данных

1. Войдите в phpMyAdmin или MySQL консоль
2. Создайте новую базу данных:
```sql
CREATE DATABASE astrogenix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. Импорт схемы базы данных

**Вариант A: Простая установка (рекомендуется)**
```sql
-- Используйте файл schema_simple.sql
-- В phpMyAdmin: Импорт -> Выберите файл database/schema_simple.sql
```

**Вариант B: Полная установка с внешними ключами**
```sql
-- Используйте файл schema.sql
-- В phpMyAdmin: Импорт -> Выберите файл database/schema.sql
```

### 3. Импорт начальных данных

```sql
-- В phpMyAdmin: Импорт -> Выберите файл database/initial_data.sql
```

### 4. Настройка конфигурации

Отредактируйте файл `config/database.php`:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'astrogenix');
define('DB_USER', 'ваш_пользователь');
define('DB_PASS', 'ваш_пароль');
```

### 5. Создание администратора

**Способ 1: Через PHP скрипт**
```bash
# Перейдите в папку database и выполните:
php create_admin.php
```

**Способ 2: Через SQL запрос**
```sql
INSERT INTO admin_users (username, email, password, role, is_active) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'super_admin', 1);
```

### 6. Настройка безопасности

Обновите ключи безопасности в `config/config.php`:

```php
define('ENCRYPTION_KEY', 'ваш-уникальный-ключ-шифрования');
define('JWT_SECRET', 'ваш-уникальный-jwt-секрет');
define('PASSWORD_SALT', 'ваша-уникальная-соль');
```

### 7. Настройка прав доступа

```bash
chmod 755 assets/
chmod 755 uploads/
chmod 644 config/*.php
```

## 🔧 Настройка веб-сервера

### Apache (.htaccess)

Создайте файл `.htaccess` в корне проекта:

```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Безопасность
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# Запрет доступа к конфигурационным файлам
<Files "*.php">
    <RequireAll>
        Require all denied
        Require local
    </RequireAll>
</Files>

# Разрешить доступ к основным файлам
<Files "index.php">
    Require all granted
</Files>

<Files "login.php">
    Require all granted
</Files>

<Files "register.php">
    Require all granted
</Files>

<Files "logout.php">
    Require all granted
</Files>
```

### Nginx

```nginx
server {
    listen 80;
    server_name ваш-домен.com;
    root /path/to/astrogenix;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Безопасность
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";

    # Запрет доступа к конфигурационным файлам
    location ~ ^/(config|includes|database)/ {
        deny all;
    }
}
```

## 🔐 Доступ к админ-панели

После установки:

- **URL**: `ваш-домен.com/admin/`
- **Логин**: `admin`
- **Пароль**: `admin123`

**⚠️ ВАЖНО**: Обязательно смените пароль после первого входа!

## 🛠️ Устранение неполадок

### Ошибка подключения к базе данных
- Проверьте настройки в `config/database.php`
- Убедитесь, что база данных создана
- Проверьте права пользователя MySQL

### Ошибка "Cannot modify header information"
- Убедитесь, что нет пробелов или символов до `<?php`
- Проверьте кодировку файлов (должна быть UTF-8 без BOM)

### Ошибка 500 Internal Server Error
- Проверьте логи ошибок веб-сервера
- Убедитесь, что PHP модули установлены (PDO, PDO_MySQL)
- Проверьте права доступа к файлам

### Проблемы с внешними ключами
- Используйте `schema_simple.sql` вместо `schema.sql`
- Или отключите проверку внешних ключей:
```sql
SET FOREIGN_KEY_CHECKS = 0;
-- ваши запросы
SET FOREIGN_KEY_CHECKS = 1;
```

## 📋 Требования к системе

- **PHP**: 7.4 или выше
- **MySQL**: 5.7 или выше
- **Веб-сервер**: Apache или Nginx
- **PHP расширения**: PDO, PDO_MySQL, JSON, OpenSSL, mbstring

## 🔄 Обновление

1. Сделайте резервную копию базы данных
2. Сделайте резервную копию файлов
3. Загрузите новые файлы
4. Выполните миграции базы данных (если есть)
5. Очистите кеш (если используется)

## 📞 Поддержка

Если возникли проблемы:

1. Проверьте логи ошибок
2. Убедитесь, что все требования выполнены
3. Проверьте настройки конфигурации
4. Обратитесь за помощью с подробным описанием ошибки

---

**Успешной установки! 🚀**
