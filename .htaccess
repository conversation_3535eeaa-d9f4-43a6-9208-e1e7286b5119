# AstroGenix .htaccess Configuration
# Security and URL rewriting rules

# Enable rewrite engine
RewriteEngine On

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Prevent access to sensitive files
<Files "*.php">
    Order Allow,Deny
    Allow from all
</Files>

# Block access to configuration and include directories
<IfModule mod_rewrite.c>
    RewriteRule ^(config|includes|database)/ - [F,L]
</IfModule>

# Block access to specific files
<Files "*.sql">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.log">
    Order Deny,Allow
    Deny from all
</Files>

<Files ".htaccess">
    Order Deny,Allow
    Deny from all
</Files>

# Allow specific PHP files
<Files "index.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "login.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "register.php">
    Order Allow,<PERSON>y
    Allow from all
</Files>

<Files "logout.php">
    Order Allow,<PERSON>y
    Allow from all
</Files>

<Files "simple_test.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "check_system.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "test_db.php">
    Order Allow,Deny
    Allow from all
</Files>

# Allow access to pages directory
<IfModule mod_rewrite.c>
    RewriteRule ^pages/ - [L]
</IfModule>

# Allow access to API directory
<IfModule mod_rewrite.c>
    RewriteRule ^api/ - [L]
</IfModule>

# Allow access to assets
<IfModule mod_rewrite.c>
    RewriteRule ^assets/ - [L]
</IfModule>

# URL rewriting for clean URLs (if needed in future)
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteCond %{REQUEST_FILENAME} !-d
# RewriteRule ^(.*)$ index.php [QSA,L]

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache control
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/x-icon "access plus 1 month"
    ExpiresByType application/x-icon "access plus 1 month"
</IfModule>

# Error pages (optional)
# ErrorDocument 404 /404.php
# ErrorDocument 500 /500.php
