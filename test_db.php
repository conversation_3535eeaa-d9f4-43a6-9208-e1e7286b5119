<?php
/**
 * AstroGenix - Database Connection Test
 * Simple test to check database connectivity
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>AstroGenix Database Test</h1>";

try {
    // Include database configuration
    require_once 'config/database.php';
    
    echo "<p>✅ Database configuration loaded successfully</p>";
    
    // Test database connection
    $db = getDB();
    echo "<p>✅ Database connection established</p>";
    
    // Test basic query
    $stmt = $db->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "<p>✅ Users table accessible. Total users: " . $result['count'] . "</p>";
    
    // Test admin users
    $stmt = $db->query("SELECT COUNT(*) as count FROM admin_users");
    $result = $stmt->fetch();
    echo "<p>✅ Admin users table accessible. Total admins: " . $result['count'] . "</p>";
    
    // Test investment packages
    $stmt = $db->query("SELECT COUNT(*) as count FROM investment_packages");
    $result = $stmt->fetch();
    echo "<p>✅ Investment packages table accessible. Total packages: " . $result['count'] . "</p>";
    
    // Test site settings
    $stmt = $db->query("SELECT COUNT(*) as count FROM site_settings");
    $result = $stmt->fetch();
    echo "<p>✅ Site settings table accessible. Total settings: " . $result['count'] . "</p>";
    
    echo "<h2>✅ All database tests passed!</h2>";
    echo "<p><a href='index.php'>Go to main page</a></p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Database Error:</h2>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    
    echo "<h3>Troubleshooting:</h3>";
    echo "<ul>";
    echo "<li>Check database credentials in config/database.php</li>";
    echo "<li>Make sure MySQL server is running</li>";
    echo "<li>Verify database 'astrogenix' exists</li>";
    echo "<li>Import the database schema from database/astrogenix_complete.sql</li>";
    echo "</ul>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>AstroGenix Database Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h1 { color: #1e3a8a; }
        h2 { color: #059669; }
        .error { color: #dc2626; }
        ul { background: #f3f4f6; padding: 20px; border-radius: 8px; }
    </style>
</head>
<body>
</body>
</html>
